module API
  class PluginSettingsController < BaseController
    include <PERSON><PERSON><PERSON><PERSON>
    before_action :authenticate_user!
    before_action :set_plugin_setting

    # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength
    def xhr_select
      credentials = (@plugin_setting&.encrypted_settings || {}).merge(current_user.credentials)

      json = case params[:function]
             when 'basecamp_account'
               access_token = credentials.dig('basecamp', 'access_token')
               return {} unless access_token

               Plugins::Basecamp.list_account(access_token)
             when 'basecamp_project'
               access_token = credentials.dig('basecamp', 'access_token')
               return {} unless access_token

               puts params[:plugin_setting][:settings_basecamp_account]
               Plugins::Basecamp.list_projects(access_token, params[:plugin_setting][:settings_basecamp_account])
             when 'calendar'
               return {} unless credentials["google_calendar"]

               Plugins::GoogleCalendar.list_calendar(credentials)
             when 'close_pipeline'
               access_token = credentials.dig('close', 'access_token')
               return {} unless access_token

               response = HTTParty.get('https://api.close.com/api/v1/pipeline/', headers: { "Authorization" => "Bearer #{access_token}" })
               response.parsed_response['data'].map do |pipeline|
                 pipeline['statuses'].flatten.map { |s| { "#{pipeline['name']}: #{s['label']}" => s['id'] } }
               end.flatten
             when 'google_tasks_list'
               return {} unless credentials["google_tasks"]

               Plugins::GoogleTasks.list_tasklist(credentials)
             when 'list_id'
               return {} unless credentials["mailchimp_analytics"]

               Plugins::MailchimpAnalytics.fetch_lists(credentials["mailchimp_analytics"]['access_token'])
             when 'todoist_project_id'
               access_token = credentials.dig('todoist', 'access_token')
               return {} unless access_token

               Plugins::Todoist.projects(access_token)
             when 'todoist_filter_label_ids'
               access_token = credentials.dig('todoist', 'access_token')
               return {} unless access_token

               Plugins::Todoist.labels(access_token)
             when 'ticktick_project_id'
               access_token = credentials.dig('ticktick', 'access_token')
               return {} unless access_token

               Plugins::Ticktick.projects(access_token)
             when 'square_location'
               access_token = credentials.dig('square_pos', 'access_token')
               return {} unless access_token

               response = HTTParty.get(Square.location_url, headers: { "Authorization" => "Bearer #{access_token}" })
               response.parsed_response['locations'].map { |m| { m['name'] => m['id'] } }
             when 'mlb_team_id'
               response = HTTParty.get('https://statsapi.mlb.com/api/v1/teams')
               response['teams'].filter { |t| [103, 104].include?(t.dig('league', 'id')) }.map { |t| { t['name'] => t['id'] } } # American, National league
             when 'polymarket_tag_id'
               Plugins::Polymarket::TAGS
             when 'hubspot_pipeline_id'
               return {} unless credentials['hubspot']

               Plugins::Hubspot.pipelines(credentials['hubspot'])
             when 'hubspot_deal_stages'
               return {} unless credentials['hubspot']

               pipeline_id = params.dig(:plugin_setting, :settings_hubspot_pipeline_id) || @plugin_setting.settings['hubspot_pipeline_id']
               Plugins::Hubspot.deal_stages(pipeline_id, credentials['hubspot'])
             when 'upcoming_movies_region'
               Plugins::UpcomingMovies.regions
             when 'salesforce_opportunity_views'
               return {} unless credentials["salesforce"]

               Plugins::Salesforce.opportunity_views(credentials["salesforce"])
             when 'tempest_weather_station_devices'
               return {} unless credentials["tempest_weather_station"]

               Plugins::TempestWeatherStation.devices(credentials['tempest_weather_station'])
             end

      render json:
    end
    # rubocop:enable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity, Metrics/MethodLength

    def xhr_select_search
      credentials = (@plugin_setting&.encrypted_settings || {}).merge(current_user.credentials)
      search_query = params[:query] || ""

      results = case params[:function]
                when 'notion_database_id'
                  return render json: [] unless credentials["notion"]

                  Plugins::Notion.search_databases(credentials, search_query)
                when 'notion_page_id'
                  return render json: [] unless credentials["notion"]

                  Plugins::Notion.search_pages(credentials, search_query)
                else
                  []
                end

      render json: results
    end

    def xhr_function
      # lookup in collection of instance methods vs respond_to?() because this would yield 'true' for destructive actions (delete/destroy)
      @plugin_setting.service.send(params[:function]) if @plugin_setting.service.class.instance_methods.include?(params[:function]&.to_sym)

      render json: { status: :ok }
    end

    private

    def set_plugin_setting
      @plugin_setting = current_user.plugin_settings.find_by(id: params[:plugin_setting][:id])
    end
  end
end
