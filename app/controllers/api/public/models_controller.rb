module API
  module Public
    class ModelsController < BaseController
      def index
        render_ok(models_json)
      end

      private

      def models_json
        DeviceModel.visible_model_specs.map do |spec|
          {
            name: spec[:keyname],
            label: spec[:name],
            description: spec[:description] || spec[:name],
            width: spec[:width],
            height: spec[:height],
            colors: spec[:colours],
            bit_depth: spec[:colour_depth],
            scale_factor: spec[:scale_factor] || 1.0,
            rotation: spec[:rotate] || 0,
            mime_type: mime_type(spec[:format]),
            offset_x: spec[:offset_x] || 0,
            offset_y: spec[:offset_y] || 0,
            published_at: spec[:published_at]
          }
        end.as_json
      end

      def mime_type(format)
        case format
        when 'png'
          'image/png'
        when 'bmp3'
          'image/bmp'
        else
          raise "Unknown format: #{format}"
        end
      end
    end
  end
end
