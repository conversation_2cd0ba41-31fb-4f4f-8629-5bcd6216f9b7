# frozen_string_literal: true

require 'googleauth'
require 'google/apis/calendar_v3'

class PluginSettingsController < ApplicationController
  include Oauthable
  before_action :authenticate_user!, except: [:reinit]
  before_action :set_plugin_setting, only: [:edit, :update, :destroy, :add_to_playlist, :copy, :export, :configure, :reset_health, :enable_debug_logs, :preview_field]
  before_action :set_plugin, only: [:index, :new, :edit, :update, :export, :configure, :reset_credentials]
  before_action :maybe_force_refresh, only: [:edit]
  before_action :maybe_require_upgrade, only: [:new, :export], if: -> { @plugin.private? }
  before_action :handle_canceled_consent, only: [:redirect]
  before_action :check_import_file_size, only: [:create]

  def index
    @plugin_settings = current_user.plugin_settings.where(plugin_id: @plugin.id).alphabetical
    @import_plugin_setting = current_user.plugin_settings.new(plugin_id: @plugin.id) if @plugin.archivable?
    redirect_to new_plugin_setting_path(keyname: @plugin.keyname) unless @plugin_settings.count.positive?
  end

  def new
    @plugin_setting = current_user.plugin_settings.new
  end

  def create
    plugin_setting = current_user.plugin_settings.new(plugin_settings_params)
    plugin_setting.encrypted_settings = plugin_settings_encrypted_params['encrypted_settings'] || {}

    plugin = plugin_setting.plugin
    if plugin.oauth2? && plugin.native?
      plugin_setting.encrypted_settings[plugin.keyname] = current_user.credentials[plugin.keyname]
    end

    return unless maybe_import_archive(plugin, plugin_setting)

    if plugin_setting.save
      maybe_fork_recipe_markup(plugin_setting)

      if plugin.oauth2? && plugin.native?
        current_user.update(credentials: current_user.credentials.except(plugin.keyname))
      elsif plugin.third_party?
        token_store = OauthTokenStore.find_by_code(params[:plugin_setting][:code])
        token_store.update(plugin_setting_id: plugin_setting.id, plugin_id: nil)
      end
      current_device.playlist_items.create!(plugin_setting_id: plugin_setting.id, playlist_group_id: current_device.default_playlist_group.id)
      redirect_to edit_plugin_setting_path(plugin_setting.id), notice: "#{t('.success')} #{t('.playlist')}"
    else
      redirect_to new_plugin_setting_path(keyname: plugin.keyname), alert: plugin_setting.errors.full_messages.to_sentence
    end
  end

  def edit
    session[:return_to] = request.referer if %w[dashboard playlists].any? { request.referer&.include?(it) }
  end

  def update
    @plugin = @plugin_setting.plugin

    if @plugin_setting.update(plugin_settings_params)
      if @plugin.oauth2? && current_user.credentials[@plugin.keyname].present?
        @plugin_setting.encrypted_settings.merge!(@plugin.keyname => current_user.credentials[@plugin.keyname])
        @plugin_setting.save
        current_user.update(credentials: current_user.credentials.except(@plugin.keyname))
      end
      if params[:plugin_setting][:encrypted_settings].present?
        old_settings = @plugin_setting.encrypted_settings
        @plugin_setting.update(encrypted_settings: old_settings.merge!(plugin_settings_encrypted_params['encrypted_settings'] || {}))
      end
      redirect_to edit_plugin_setting_path(@plugin_setting.id), notice: t('.success')
    else
      redirect_to edit_plugin_setting_path(@plugin_setting.id), alert: @plugin_setting.errors.full_messages.to_sentence
    end
  end

  def redirect
    process_oauth_callback(params, current_user)
    respond_to do |format|
      format.html do
        redirect_to new_plugin_setting_path(keyname: params[:id])
      end
      format.json do
        render json: { location: new_plugin_setting_path(keyname: params[:id]) }
      end
    end
  end

  def configure
    redirect_to @plugin.configuration_url(@plugin_setting), allow_other_host: true
  end

  def reinit; end

  def reset_credentials
    current_user.update(credentials: current_user.credentials.except(@plugin.keyname))
    current_user.plugin_settings.includes(:mashups).find_by(id: params[:id])&.destroy

    redirect_to new_plugin_setting_path(keyname: @plugin.keyname), notice: t('plugin_settings.reset_credentials', plugin_name: @plugin.name)
  end

  def force_refresh
    plugin_setting = current_user.plugin_settings.find_by_uuid(params[:id])
    Plugins::ForceRefreshWorker.perform_async(plugin_setting&.id) unless plugin_setting&.plugin&.global?
  end

  def enable_debug_logs
    @plugin_setting.enable_debug_logs!
  end

  def destroy
    if @plugin_setting.destroy
      redirect_to plugins_path, notice: t('.success')
    end
  end

  def add_to_playlist
    if current_device&.playlist_items&.create!(plugin_setting_id: @plugin_setting.id, playlist_group_id: current_device.default_playlist_group.id)
      flash[:notice] = t('.success')
    else
      flash[:alert] = "Error adding to playlist"
    end
    render partial: "shared/flash_turbo"
  end

  def copy
    @plugin_setting&.copy!
  end

  def export
    if @plugin.archivable?
      archiver = PrivatePluginArchiver.new(@plugin_setting)
      send_data archiver.export.read, filename: archiver.archive_filename, type: archiver.mime_type
    else
      raise ActionController::RoutingError, 'Not Found'
    end
  end

  def reset_health
    @plugin_setting.update(state: :healthy, error_retry_count: 0, error_message: nil)
    flash[:notice] = "Plugin health reset successfully."
    redirect_to edit_plugin_setting_path(@plugin_setting.id)
  end

  def preview_field
    field_template = params[:field_template]
    field_key = params[:field_key]

    # Check for required parameters
    unless field_template && field_key
      return render json: {
        error: 'Missing required parameters: field_template or field_key',
        success: false
      }, status: 422
    end

    custom_fields_values = plugin_settings_params[:settings][:custom_fields_values].to_h

    begin
      # Create the main plugin service to get global variables
      plugin_service = @plugin_setting.service
      return render json: { error: 'Plugin service not available', success: false }, status: 422 unless plugin_service

      global_vars = plugin_service.send(:global_variables)

      # Create a temporary data service to process the field template
      data_service = Plugins::Data::PrivatePlugin.new(
        @plugin_setting,
        global_vars,
        custom_fields_values
      )

      # Use existing Liquid parser to process the template
      postprocessor = 'string_to_hash' if field_key == 'polling_headers'
      processed_content = data_service.send(:liquify, field_template, postprocessor:)

      render json: { preview_content: processed_content, success: true }
    rescue StandardError => e
      render json: { error: e.message, success: false }, status: 422
    end
  end

  private

  def set_plugin_setting
    @plugin_setting = current_user.plugin_settings.includes(:mashups).find_by(id: params[:id])
    redirect_to plugins_path unless @plugin_setting
  end

  def set_plugin
    @plugin = @plugin_setting&.plugin || Plugin.find_by_keyname(params[:keyname])

    if current_user.admin?
      # do nothing, user can view or connect to all plugins
    elsif !Plugin.publicly_available?(current_user.id, @plugin&.keyname)
      redirect_to plugins_path, alert: t(:plugin_not_found)
    end
  end

  def plugin_settings_params
    params.require(:plugin_setting).permit(:name, :refresh_interval, :plugin_id, :upstream_id, :icon, settings: {})
  end

  def plugin_settings_encrypted_params
    params.require(:plugin_setting).permit(encrypted_settings: {})
  end

  def handle_canceled_consent
    redirect_to new_plugin_setting_path(keyname: params[:id]), alert: t('.handle_canceled_consent') unless [:code, :public_token].any? { |key| params.key?(key) }
  end

  def check_import_file_size
    if params.dig(:plugin_setting, :import_file)&.size.to_i > PrivatePluginArchiver::MAX_ZIP_FILE_SIZE
      redirect_back alert: t('.import.archive_too_large', max_mb: PrivatePluginArchiver::MAX_ZIP_FILE_SIZE / 1.megabyte), fallback_location: new_plugin_setting_path(keyname: @plugin.keyname)
    end
  end

  # Returns true if import is successful or no import is needed. Returns false if import fails.
  def maybe_import_archive(plugin, plugin_setting)
    if plugin.archivable? && params.dig(:plugin_setting, :import_file).present?
      archiver = PrivatePluginArchiver.new(plugin_setting)
      unless archiver.import(params[:plugin_setting][:import_file])
        redirect_to plugin_settings_path(keyname: plugin.keyname), alert: plugin_setting.errors.full_messages.to_sentence
        return false
      end
    end

    true
  end

  def maybe_fork_recipe_markup(plugin_setting)
    return if plugin_setting.read_only?
    return unless plugin_setting.fork?

    plugin_setting.copy_markup_from(plugin_setting.upstream)
  end

  # UX improvement for 3rd party plugins, when user visits Management Portal to change settings
  def maybe_force_refresh
    if params[:force_refresh] == 'true' && @plugin.third_party?
      Plugins::ForceRefreshWorker.perform_async(@plugin_setting&.id)
      redirect_to edit_plugin_setting_path(@plugin_setting.id), notice: t('plugin_settings.refreshing_now_please_wait')
    end
  end
end
