module Previewable
  extend ActiveSupport::Concern

  include Demoable
  include Plugins::Helpers::HtmlRenderable

  def generate_preview!
    img_path = Converter::Html.new(html: demo_html_document, model_id:, image: false, plugin: 'preview').process

    preview_path = Rails.root.join('app', 'assets', 'images', 'plugin_previews', "#{params[:id]}.png")
    IO.copy_stream(img_path, preview_path)
  end

  def demo_html_document
    locals = get_demo_data(params[:id], params[:variant]).deep_merge({ no_screen_padding: params[:no_screen_padding] })
    render_plugin_html(I18n.default_locale, template: "plugins/#{params[:id]}/full", locals: locals)
  end

  def model_id
    DeviceModel.find_by_keyname('og_png').id
  end
end
