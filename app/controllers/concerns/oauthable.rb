module Oauthable
  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def process_oauth_callback(params, user)
    plugin_keyname = params[:id]

    # Try OAuth2 service first
    oauth_service = oauth2_service_for(plugin_keyname)
    if oauth_service
      oauth_service.process_callback(params[:code], user)
      return
    end

    # Fall back to legacy switch statement
    case plugin_keyname
    when 'basecamp'
      token = Plugins::Basecamp.fetch_access_token(params[:code])
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => { access_token: token[:access_token], refresh_token: token[:refresh_token] }))
    when 'close'
      token = Plugins::Close.fetch_access_token(params[:code])
      credentials = user.credentials
      user.update(
        credentials: credentials.merge!(params[:id] => {
          access_token: token[:access_token],
          refresh_token: token[:refresh_token]
        })
      )
    when 'google_analytics'
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => Plugins::GoogleAnalytics.fetch_access_token(params[:code])))
    when 'google_calendar'
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => Plugins::GoogleCalendar.fetch_access_token(params[:code])))
    when 'google_tasks'
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => Plugins::GoogleTasks.fetch_access_token(params[:code])))
    when 'gumroad_analytics'
      token = Plugins::GumroadAnalytics.fetch_access_token(params[:code])
      credentials = user.credentials
      user.update(
        credentials: credentials.merge!(params[:id] => {
          access_token: token[:access_token],
          refresh_token: token[:refresh_token]
        })
      )
    when 'hubspot'
      token = Plugins::Hubspot.fetch_access_token(params[:code])
      credentials = user.credentials
      credentials[params[:id]] = {} if credentials[params[:id]].nil?
      credentials[params[:id]] = token
      user.update(credentials:)
    when 'mailchimp_analytics'
      credentials = user.credentials
      access_token = Plugins::MailchimpAnalytics.fetch_access_token(params[:code])
      user.update(credentials: credentials.merge!(params[:id] => { access_token: access_token }))
    when 'reddit'
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => Plugins::Reddit.fetch_access_token(params[:code])))
    when 'robinhood_portfolio'
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => { public_token: params[:public_token] }))
    when 'salesforce'
      token = Plugins::Salesforce.fetch_access_token(params[:code])
      credentials = user.credentials
      user.update(
        credentials: credentials.merge!(params[:id] => {
          access_token: token[:access_token],
          refresh_token: token[:refresh_token]
        })
      )
    when 'square_pos'
      token = Plugins::SquarePos.fetch_access_token(params[:code])
      credentials = user.credentials
      user.update(
        credentials: credentials.merge!(params[:id] => {
          access_token: token[:access_token],
          refresh_token: token[:refresh_token]
        })
      )
    when 'todoist'
      credentials = user.credentials
      user.update(
        credentials: credentials.merge!(params[:id] => { access_token: Plugins::Todoist.fetch_access_token(params[:code]) })
      )
    when 'ticktick'
      credentials = user.credentials
      user.update(
        credentials: credentials.merge!(params[:id] => { access_token: Plugins::Ticktick.fetch_access_token(params[:code]) })
      )
    when 'tempest_weather_station'
      token = Plugins::TempestWeatherStation.fetch_access_token(params[:code])
      credentials = user.credentials
      credentials[params[:id]] = {} if credentials[params[:id]].nil?
      credentials[params[:id]] = token
      user.update(credentials:)
    when 'youtube_analytics'
      credentials = user.credentials
      user.update(credentials: credentials.merge!(params[:id] => Plugins::YoutubeAnalytics.fetch_access_token(params[:code])))
    end
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  private

  def oauth2_service_for(plugin_keyname)
    service_class = "OauthService::#{plugin_keyname.camelize}".safe_constantize
    service_class&.new(plugin_keyname)
  rescue StandardError
    nil
  end
end
