module PluginExampleData
  extend ActiveSupport::Concern

  def custom_plugin_example_data
    books = [{ title: 'Book title', description: 'Some description' }, { title: 'Another book', description: 'Another description' }]

    data = if params[:collection_only]
             books
           else
             { text: "Random text #{rand(100.500)} from web server", author: 'TRMNL', collection: books }
           end

    if request.headers['authorization'] == 'token qwerty'
      secret_data = { secret_data: 'for your eyes only' }
      data.is_a?(Array) ? data << secret_data : data.merge!(secret_data)
    end

    render json: data
  end

  def custom_plugin_example_xhr_select
    render json: [{ 'Braves' => 123 }, { 'Yankees' => 456 }]
  end

  def custom_plugin_example_xhr_select_search
    data = [
      { 'id' => 'db-123', 'name' => 'Project Tasks' },
      { 'id' => 'db-456', 'name' => 'Team Goals' },
      { 'id' => 'db-789', 'name' => 'Family Goals' }
    ]

    results = params[:query].present? ? data.filter { |opt| opt['name'].downcase.include?(params[:query].downcase) } : data
    render json: results
  end
end
