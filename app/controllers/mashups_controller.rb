class MashupsController < ApplicationController
  before_action :authenticate_user!
  skip_before_action :verify_authenticity_token, only: [:create]

  def create
    @mashup = Mashup.create(user_id: current_user.id, layout: params[:layout])
    current_device.create_default_playlist_group if current_device.playlist_groups_count.zero?
    @playlist_item = PlaylistItem.find_or_create_by(mashup_id: @mashup.id, device_id: current_device.id, playlist_group_id: current_device.playlist_groups.orderable.first.id)

    @plugin_settings = current_user.plugin_settings
                                   .where.not(plugin_id: incompatible_mashup_plugin_ids)

    @plugin_settings += installed_recipe
    @plugin_settings = @plugin_settings.uniq.sort_by { |ps| ps.plugin.name }
  end

  def edit
    @mashup = Mashup.find(params[:id])
    @playlist_item = PlaylistItem.find(params[:playlist_id])

    @plugin_settings = current_user.plugin_settings
                                   .where.not(plugin_id: incompatible_mashup_plugin_ids)

    @plugin_settings += installed_recipe
    @plugin_settings = @plugin_settings.sort_by { |ps| ps.plugin.name }
  end

  private

  def installed_recipe
    PluginSetting.joins(:playlist_items)
                 .where(playlist_items: { device_id: current_device.id })
                 .where(recipe_status: :published)
                 .distinct
  end

  def incompatible_mashup_plugin_ids
    Rails.cache.fetch("incompatible_mashup_plugin_ids", expire_in: 30.minutes) { Plugin.active.mashup_incompatible.pluck(:id) }
  end
end
