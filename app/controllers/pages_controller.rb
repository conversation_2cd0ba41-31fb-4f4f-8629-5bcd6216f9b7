require 'socket'

class PagesController < ApplicationController
  include PluginExampleData
  before_action :authenticate_user!, only: [:logout]
  before_action :set_referral_code, only: [:home]
  skip_before_action :verify_authenticity_token, only: [:custom_plugin_example_xhr_select]

  def home
    @testimonials = Reviews.call
    @videos = Videos.call
    render layout: 'lander'
  end

  def about
    @og_image = 'og/about.jpg'
    @og_description = 'Meet the team behind TRMNL - building the future of calm computing.'
    render layout: 'lander'
  end

  def bounties
    @og_image = 'og/bounties.jpg'
    @og_description = 'Get paid to make TRMNL better.'
    @bounties = params[:completed] ? Bounty.newest_to_oldest.completed : Bounty.newest_to_oldest.available
  end

  def m2
    @og_image = 'og/mashups-v2.png'
    @og_description = 'A new way to Mashup. Coming soon.'
  end

  def buy
    session[:open_checkout_modal] = true
    redirect_to root_path
  end

  def claim_a_device
    render layout: 'lander'
  end

  def hackathon
    @og_image = 'og/hackathons.jpg'
    @og_description = 'Start building or submit your work.'
    @plugin_settings = current_user.plugin_settings.where(plugin_id: 37) if current_user # private plugins may be submitted
    render layout: 'lander'
  end

  def find_my_device
    @og_description = 'Finish your TRMNL setup.'
    @missing_device = Device.new
    render layout: 'lander'
  end

  def fortune_500
    @og_image = 'landing/fortune-500-brand-logos.png'
    @og_description = 'Free lunch (device) for the people who need it most.'
    render layout: 'lander'
  end

  def flash_firmware
    @og_description = 'Update your device firmware in 1 click.'
    @og_image = 'og/flash.jpg'
    render layout: 'lander'
  end

  def brand
    render layout: 'lander'
  end

  def roadmap
    roadmap_url = 'https://trello.com/b/efhMgP6E/trmnl-public-roadmap'
    redirect_to roadmap_url, allow_other_host: true
  end

  def terms
    render layout: 'lander'
  end

  def privacy
    render layout: 'lander'
  end

  def developers
    @og_image = 'og/developers.jpg'
    @og_description = 'Custom plugins, self-hosted servers, and more.'
    render layout: 'lander'
  end

  def logout
    sign_out(current_user)
    redirect_to root_path
  end

  def third_party_plugin_example_install
    redirect_to params[:installation_callback_url]
  end

  def page
    @page_key = request.path[1..]
    render "pages/#{@page_key}"
  end

  def up
    render plain: Socket.gethostname
  end
end
