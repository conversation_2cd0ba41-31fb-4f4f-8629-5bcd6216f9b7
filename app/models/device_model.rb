class DeviceModel < ApplicationRecord
  include DeviceModelSpecs

  validates :keyname, presence: true, uniqueness: true
  validates :width, presence: true, numericality: { greater_than: 0 }
  validates :height, presence: true, numericality: { greater_than: 0 }
  validates :colour_depth, presence: true, numericality: { greater_than: 0 }
  validates :format, presence: true

  enum :device_type, [:trmnl, :kindle, :byod]

  has_many :devices

  scope :visible, -> { where(visible: true) }
  scope :hidden, -> { where(visible: false) }

  def as_json(_options = nil)
    { name:, width:, height:, colour_depth:, colours:, format:, scale_factor:, rotate: }
  end

  def self.cached_find(id)
    Rails.cache.fetch("device_model/#{id}", expire: 1.days, ignore_nil: true) { DeviceModel.find(id).as_json }
  end

  def self.all_model_ids
    Rails.cache.fetch("device_model/all_model_ids", expire: 1.days, ignore_nil: true) { DeviceModel.all.pluck(:id) }
  end

  def self.all_byod_devices
    Rails.cache.fetch("device_model/all_byod_devices", expire: 1.days, ignore_nil: true) do
      byod_device_options(DeviceModel)
    end
  end

  def self.visible_byod_devices
    Rails.cache.fetch("device_model/visible_byod_devices", expire: 1.days, ignore_nil: true) do
      byod_device_options(DeviceModel.visible)
    end
  end

  def self.byod_device_options(scope)
    scope.where(device_type: [:kindle, :byod]).or(DeviceModel.where(keyname: 'og_png')).order(:name).map { ["#{it.name} - #{it.width}x#{it.height}", it.id] }
  end
end
