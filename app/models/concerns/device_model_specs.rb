# rubocop:disable Metrics/ModuleLength
module DeviceModelSpecs
  extend ActiveSupport::Concern

  class_methods do
    def all_model_specs = MODEL_SPECS
    def visible_model_specs = MODEL_SPECS.select { |spec| spec[:visible] }

    # Returns contents to be inserted into app/assets/stylesheets/framework/config/_devices.scss
    def devices_scss
      devices =
        MODEL_SPECS.map do |spec|
          <<~SCSS
            '#{spec.dig(:css, :keyname)}': (
                'screen-w': #{spec.dig(:css, :screen_w)}px,
                'screen-h': #{spec.dig(:css, :screen_h)}px,
                'pixel-ratio': #{spec[:scale_factor]},
                'ui-scale': #{spec.dig(:css, :ui_scale)},
                'color-depth': #{spec[:colour_depth]},
                'gap-scale': #{spec.dig(:css, :gap_scale)},
                'size': '#{spec.dig(:css, :size)}'
            ),
          SCSS
        end.join

      <<~SCSS
        // ============================================
        // TRMNL Framework - Device Configurations
        // ============================================
        //
        // DO NOT MANUALLY EDIT THIS FILE!
        //
        // Make changes to app/models/concerns/device_model_specs.rb, then run:
        //
        //     rake css:sync
        //

        $devices: (
        #{devices.strip.indent(4)}
        );
      SCSS
    end
  end

  def self.define_model(attrs)
    model_defaults = {
      scale_factor: 1.0,
      format: 'png',
      rotate: 0,
      offset_x: 0,
      offset_y: 0,
      published_at: DateTime.new(2024, 1, 1, 0, 0, 0).utc,
      visible: true
    }

    css_defaults = {
      keyname: attrs[:keyname],
      screen_w: attrs[:width],
      screen_h: attrs[:height],
      ui_scale: 1.0,
      gap_scale: 1.0,
      size: 'md',
      bit_depth: attrs[:colour_depth]
    }

    css = css_defaults.merge(attrs[:css] || {})

    model_defaults.merge(attrs).merge(css: css)
  end

  MODEL_SPECS = [
    {
      id: 1,
      keyname: 'og',
      name: 'TRMNL OG',
      width: 800,
      height: 480,
      colour_depth: 1,
      colours: 2,
      format: 'bmp3',
      visible: false
    },
    {
      id: 2,
      keyname: 'v2',
      name: 'TRMNL Pro',
      width: 1872,
      height: 1404,
      colour_depth: 4,
      colours: 16,
      scale_factor: 1.8,
      visible: false,
      css: {
        screen_w: 1040,
        screen_h: 780,
        size: 'lg'
      }
    },
    {
      id: 3,
      keyname: 'og_png',
      name: 'TRMNL OG',
      width: 800,
      height: 480,
      colour_depth: 1,
      colours: 2
    },
    {
      id: 4,
      keyname: 'amazon_kindle_2024',
      name: 'Amazon Kindle 2024',
      width: 1400,
      height: 840,
      colour_depth: 8,
      colours: 256,
      scale_factor: 2.414,
      rotate: 90,
      offset_x: 75,
      offset_y: 25,
      device_type: :kindle,
      css: {
        screen_w: 600,
        screen_h: 444,
        ui_scale: 0.8,
        size: 'sm',
        bit_depth: 4
      }
    },
    {
      id: 5,
      keyname: 'amazon_kindle_paperwhite_6th_gen',
      name: 'Amazon Kindle PW 6th Gen',
      width: 1024,
      height: 768,
      colour_depth: 8,
      colours: 256,
      rotate: 90,
      device_type: :kindle,
      css: { # TODO: optimize
        bit_depth: 4
      }
    },
    {
      id: 6,
      keyname: 'amazon_kindle_paperwhite_7th_gen',
      name: 'Amazon Kindle PW 7th Gen',
      width: 1448,
      height: 1072,
      colour_depth: 8,
      colours: 256,
      rotate: 90,
      device_type: :kindle,
      css: { # TODO: optimize
        bit_depth: 4
      }
    },
    {
      id: 7,
      keyname: 'inkplate_10',
      name: 'Inkplate 10',
      width: 1200,
      height: 820,
      colour_depth: 3,
      colours: 8,
      device_type: :byod,
      css: { # TODO: optimize
        bit_depth: 2
      }
    },
    {
      id: 8,
      keyname: 'amazon_kindle_7',
      name: 'Amazon Kindle 7',
      width: 800,
      height: 600,
      colour_depth: 8,
      colours: 256,
      rotate: 90,
      device_type: :kindle,
      css: { # TODO: optimize
        bit_depth: 4
      }
    },
    {
      id: 9,
      keyname: 'mac_classic',
      name: 'Classic Macintosh',
      width: 512,
      height: 342,
      colour_depth: 1,
      colours: 2,
      format: 'bmp3',
      device_type: :byod,
      visible: false
    },
    {
      id: 10,
      keyname: 'playdate',
      name: 'Playdate',
      width: 450, # TODO: supposed to be 400, but Firefox can't go that narrow
      height: 240,
      colour_depth: 1,
      colours: 2,
      device_type: :byod,
      visible: false,
      css: {
        screen_w: 400,
        screen_h: 240
      }
    },
    {
      id: 11,
      keyname: 'inky_impression_7_3',
      name: 'Inky Impression 7.3',
      width: 800,
      height: 480,
      colour_depth: 8,
      colours: 7,
      device_type: :byod,
      css: { # TODO: optimize
        bit_depth: 4
      }
    },
    {
      id: 12,
      keyname: 'kobo_libra_2',
      name: 'Kobo Libra 2',
      width: 1680,
      height: 1264,
      colour_depth: 8,  # 8-bit framebuffer (standard on modern E-Ink readers)
      colours: 256,     # 2^8 greyscale steps
      rotate: 90,       # match typical landscape capture like the Kindle example
      device_type: :byod,
      css: { # TODO: optimize
        size: 'lg',
        bit_depth: 4
      }
    },
    {
      id: 13,
      keyname: 'amazon_kindle_oasis_2',
      name: 'Amazon Kindle Oasis 2',
      width: 1680,
      height: 1264,
      colour_depth: 8,
      colours: 256,
      rotate: 90,
      device_type: :byod,
      css: { # TODO: optimize
        size: 'lg',
        bit_depth: 4
      }
    },
    {
      id: 14,
      keyname: 'og_plus',
      name: 'TRMNL OG+',
      width: 800,
      height: 480,
      colour_depth: 2,
      colours: 4,
      visible: false,
      css: {
        keyname: 'ogv2' # TODO: standardize name
      }
    },
    {
      id: 15,
      keyname: 'kobo_aura_one',
      name: 'Kobo Aura One',
      width: 1872,
      height: 1404,
      colour_depth: 8,
      colours: 256,
      rotate: 90,
      device_type: :byod,
      css: { # TODO: optimize
        size: 'lg',
        bit_depth: 4
      }
    }
  ].map(&method(:define_model)).freeze
end
# rubocop:enable Metrics/ModuleLength
