<div class="<%= message_sage_classes %> flex">
  <div>
    <p class="font-medium text-xl font-heading">Know what you're doing?</p>
  </div>
  <div class="flex-1 text-right">
    <p>Skip to <%= link_to 'your dashboard', dashboard_index_path, target: :_blank, class: 'underline text-sage-500 dark:text-sage-200' %>.</p>
  </div>
</div>

<div class="space-y-2">
  <h2 class="font-medium text-2xl font-heading">Quickstart</h2>

  <ol class="pl-4 list-decimal space-y-2">
    <li class="font-semibold">Set your MAC address and disable firmware updates</li>
    <p>
      Visit your <%= link_to 'device settings', edit_device_path(current_device.id), target: :_blank, class: 'underline text-primary-450' %>
      and update the randomly generated MAC address with your device's MAC. Also disable OTA Updates.
    </p>

    <li class="font-semibold">Read the docs</li>
    <p>
      More detailed instructions are <%= link_to 'here', 'https://docs.usetrmnl.com/go/diy/byod', target: :_blank, class: 'underline text-primary-450' %>, including DIY kit tutorials.
    </p>
  </ol>
</div>

<div class="space-y-2">
  <h2 class="font-medium text-2xl font-heading">Slowstart ;)</h2>
  <p><%= link_to 'Flash our firmware', 'https://github.com/usetrmnl/trmnl-firmware', target: :_blank, class: 'underline text-primary-450' %> or check out these videos from the community.</p>

  <iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/Tr__8OlQQms?si=CRoWt5LW9LGhdgBM" title="E-Paper Dashboard without coding | Xiao E-Paper Display and TRMNL Firmware" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
  <iframe width="560" height="315" src="https://www.youtube-nocookie.com/embed/QAGTRrbQSBE?si=gxMKe92S6YN9YqIj" title="Seeed Studio running TRMNL Firmware" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
</div>
