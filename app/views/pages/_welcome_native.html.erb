<div class="<%= message_sage_classes %> flex">
  <div>
    <p class="font-medium text-xl font-heading">Know what you're doing?</p>
  </div>
  <div class="flex-1 text-right">
    <p>Skip to <%= link_to 'your dashboard', dashboard_index_path, target: :_blank, class: 'underline text-sage-500 dark:text-sage-200' %>.</p>
  </div>
</div>

<div class="space-y-2">
  <h2 class="font-medium text-2xl font-heading">Quickstart</h2>

  <ol class="pl-4 list-decimal space-y-2">
    <li class="font-semibold">Set up 1 or more plugins</li>
    <p>
      Visit the <%= link_to 'plugins marketplace', plugins_path, target: :_blank, class: 'underline text-primary-450' %>
      and search for something you want to see on TRMNL, like "calendar" or "weather."
      Click the icon, save your preferences, and within a few seconds the plugin should be live.
    </p>

    <li class="font-semibold">Configure a Playlist</li>
    <p>
      Visit your <%= link_to 'playlist', playlists_path, target: :_blank, class: 'underline text-primary-450' %>
      and arrange the display order of your connected plugins by clicking and holding the <%= render 'shared/icons/reorder', classes: 'inline text-primary-450' %> icon, similar to songs on a music playlist.
    </p>
    <p>
      You may also click the <%= render 'shared/icons/calendar', classes: 'inline text-primary-450' %> icon to schedule the day of week and time of day for each piece of content, explained <%= link_to 'here', 'https://help.usetrmnl.com/en/articles/11663305-playlist-scheduler', target: :_blank, class: 'underline text-primary-450' %>.
    </p>

    <li class="font-semibold">See content on your TRMNL</li>
    <p>
      Your device screen might say something like <i>"You're all caught up,"</i> even though you connected plugins and set up your playlist. That's because TRMNL devices go to sleep after each request for content.
    </p>
    <p>
      Force your TRMNL to wake up and fetch new items from your playlist by clicking the button on the back, near the power switch. This button can do other things too, explained <%= link_to 'here', 'https://help.usetrmnl.com/en/articles/9672080-special-functions', target: :_blank, class: 'underline text-primary-450' %>.
    </p>
  </ol>
</div>

<div class="space-y-2">
  <h2 class="font-medium text-2xl font-heading">Slowstart ;)</h2>
  <p class="italic">Tutorial video coming soon.</p>
</div>
