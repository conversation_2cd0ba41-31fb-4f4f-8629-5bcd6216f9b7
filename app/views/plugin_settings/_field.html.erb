<% object_name = model_name_from_record_or_class(model).param_key %>
<% attr_namespace ||= nil %>
<% field_name = field['encrypted'] ? "#{object_name}[encrypted_settings]#{attr_namespace}[#{field['keyname']}]" : "#{object_name}[settings]#{attr_namespace}[#{field['keyname']}]" %>
<% field_description = plugin_setting_field_description(field) %>
<% field_placeholder = field["placeholder"] || field["default"] || field['keyname'] %>
<% field_value = plugin_setting_field_value(model, field, attr_namespace) %>
<% field_rows = field['rows'] || 4 %>

<div class="p-6 <%= "hidden" if field['field_type'] == "hidden" %>"
     data-controller="conditional-validation"
     data-conditional-visibility-target="field"
     data-field-name="<%= field['keyname'] %>">
  <% if field['field_type'] == "hidden" %>
    <input type="hidden" name="<%= field_name %>" value="<%= field_value  %>" />
  <% end %>

  <label for="<%= field['keyname'] %>" class="<%= field_label_classes %>">
    <%= field["name"] %>
  </label>
  <p class="<%= field_description_classes %>"><%= sanitize field_description, form_field_sanitizers %></p>

  <% if field["field_type"] == 'xhrFunction' %>
    <p class="<%= field_description_classes %> text-gray-700 dark:text-white">
      <% if @plugin_setting.persisted? %>
        <%= link_to 'Perform', xhr_function_api_plugin_settings_path(plugin_setting: { id: @plugin_setting.id }, function: field["keyname"]), data: { turbo_method: :post, turbo_confirm: 'Are you sure?' }, class: 'underline inline' %>
      <% else %>
        <span class="cursor-not-allowed">(Save this plugin first)</span>
      <% end %>
    </p>
  <% end %>

  <% if field["field_type"] == 'url' %>
    <input name="<%= field_name %>"
           <%= !field['optional'] && 'required' %>
           value="<%= field_value  %>"
           class="<%= field_input_classes %>"
           type="url"
           pattern="https?://.+"
           placeholder="<%= field_placeholder %>"
           id="<%= field['keyname'] %>_input" />
  <% end %>

  <% if ['string', 'number'].include?(field["field_type"]) %>
    <%= text_field_tag field_name, field_value,
        {
          class: field_input_classes,
          type: field['field_type'] == 'string' ? 'text': 'number',
          placeholder: field_placeholder,
          required: !field['optional'],
          data: conditional_validations_data_attributes(field)
        }
    %>
  <% end %>

  <% if field["field_type"] == 'password' %>
    <div data-controller="password-visibility" class="relative">
      <input name="<%= field_name %>"
             data-password-visibility-target="input"
             <%= !field['optional'] && 'required' %>
             value="<%= field_value  %>"
             class="<%= field_input_classes %> pr-10"
             type="password"
             autocomplete="new-password"
             placeholder="<%= field_placeholder %>" />
        <button type="button"
                aria-label="Show password"
                aria-pressed="false"
                data-action="password-visibility#toggle"
                class="absolute inset-y-0 right-0 flex items-center pr-3 focus:outline-none"
                data-state="hidden"
                data-password-visibility-target="button">
          <span data-password-visibility-target="openIcon"
                class="hidden w-5 h-5 text-gray-500 hover:text-gray-300">
            <%= render "shared/icons/eye" %>
          </span>
          <span data-password-visibility-target="closedIcon"
                class="w-5 h-5 text-gray-500 hover:text-gray-300">
            <%= render "shared/icons/eye_slash" %>
          </span>
        </button>
    </div>
  <% end %>

  <% if ['date'].include?(field["field_type"]) %>
    <input name="<%= field_name %>"
           <%= !field['optional'] && 'required' %>
           value="<%= field_value == 'today' ? Date.today.to_s : field_value %>"
           class="<%= field_input_classes %> p-3"
           type="date"
           placeholder="<%= field_placeholder %>" />
  <% end %>

  <% if field["field_type"] == "text" %>
      <textarea name="<%= field_name %>"
                <%= !field['optional'].present? && 'required' %> placeholder="<%= field['placeholder'] %>" rows="<%= field_rows %>"
                class="<%= field_text_classes %>"
                id="<%= field['keyname'] %>_input"><%= field_value %></textarea>
  <% end %>

  <% if field["field_type"] == "code" %>
      <textarea name="<%= field_name %>"
                <%= !field['optional'].present? && 'required' %> placeholder="<%= field['placeholder'] %>" rows="<%= field_rows %>"
                class="<%= field_text_classes %>" style="font-family: monospace, monospace;"><%= field_value %></textarea>

    <% if field["keyname"] == 'custom_fields' %>
      <script type="text/javascript">
        var customFieldsVerifier = document.getElementsByClassName('verify_custom_fields')[0];
        if (customFieldsVerifier) {
          customFieldsVerifier.addEventListener('click', function() {
            verifyCustomFields()
          })
        }

        function verifyCustomFields() {
          let customFieldsContent = document.getElementsByName('plugin_setting[settings][custom_fields]')[0];
          $.ajax({
            url: '<%= verify_custom_fields_path %>',
            method: 'post',
            data: { custom_fields: customFieldsContent.value },
            success: function(data) {
              if (data.errors.length == 0) {
                alert('YAML looks good.');
              }
              else {
                let errorMessage = data.errors.join("\n");
                alert(errorMessage)
              }
            }
          })
        }
      </script>
    <% end %>
  <% end %>

  <% if field["field_type"] == "xhrSelect" %>
    <%# don't pre-load dropdown with values, just stub the <select> %>
    <%# instead fetch options + set "selected" via xhrFetch() %>
    <%= select_tag field_name, options_for_select([]),
       {
         class: "xhr-fetch #{field_input_classes}",
         "data-function": field["keyname"],
         "data-render-strategy": "select",
         "data-endpoint": field["endpoint"] || '/api/plugin_settings/xhr_select',
         "data-remote": !!field["endpoint"],
         prompt: field['multiple'] ? false : 'Please select...',
         required: !field['optional'].present?,
         multiple: field['multiple']
       }
    %>
  <% end %>

  <% if field["field_type"] == "xhrSelectSearch" %>
    <div data-controller="xhr-select-search"
         data-xhr-select-search-url-value="<%= field["endpoint"] || '/api/plugin_settings/xhr_select_search' %>"
         data-xhr-select-search-function-value="<%= field["keyname"] %>"
         data-xhr-select-search-value-field-value="<%= field["value_field"] || 'id' %>"
         data-xhr-select-search-label-field-value="<%= field["label_field"] || 'name' %>"
         data-xhr-select-search-search-field-value="<%= field["search_field"] || 'name' %>"
         data-xhr-select-search-placeholder-value="<%= field["placeholder"] || 'Select or search...' %>"
         data-xhr-select-search-name-value="<%= field_name %>"
         data-xhr-select-search-initial-value-value="<%= field_value %>"
         <%= 'data-required="true"' if !field['optional'].present? %>
         class="relative">

      <input type="text"
             data-xhr-select-search-target="input"
             data-action="input->xhr-select-search#search focus->xhr-select-search#search blur->xhr-select-search#hideDropdown keydown->xhr-select-search#handleKeyboard"
             placeholder="<%= field["placeholder"] || 'Select or search...' %>"
             autocomplete="off"
             style="cursor: text;"
             class="block mt-2 min-h-12 px-4 rounded-xl text-black bg-white w-full outline-none text-sm border border-gray-300 transition duration-150 dark:text-white dark:bg-gray-800 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent focus:ring-offset-2 focus:ring-offset-gray-200 dark:focus:ring-offset-gray-900 placeholder:text-gray-400 dark:placeholder:text-gray-500" />

      <input type="hidden"
             name="<%= field_name %>"
             data-xhr-select-search-target="hiddenInput"
             value="<%= field_value %>" />

      <div data-xhr-select-search-target="dropdown"
           class="absolute z-50 mt-1 bg-white shadow-lg rounded-lg border border-gray-300 max-h-60 overflow-auto w-full hidden dark:bg-gray-800 dark:border-gray-600">
        <%# Dropdown content will be populated by the controller %>
      </div>
    </div>
  <% end %>

  <% if field["field_type"] == "xhrString" %>
    <input name="<%= field_name %>"
            <%= !field['optional'] && 'required' %>
            value="<%= field_value %>"
            class="xhr-fetch <%= field_input_classes %>"
            type='text'
            readonly
            data-function="<%= field["keyname"] %>"
            data-render-strategy="string"
            placeholder="<%= field_placeholder %>" />
  <% end %>

  <% if field["field_type"] == "select" %>
    <%# private plugins with custom fields may forget to include, this soft fails %>
    <% options = field["options"] || [] %>
    <% select_values = options.map do |v|
        if v.is_a?(Hash)
          [v.keys.first, v.values.first]
        else
          [v, v&.to_s.downcase.gsub(' ', '_')]
        end
      end
    %>
    <%= select_tag field_name, options_for_select(select_values, field_value),
        {
          class: field_input_classes,
          prompt: field['multiple'] ? false : 'Please select...',
          required: !field['optional'].present?,
          multiple: field['multiple'],
          data: conditional_validations_data_attributes(field)
        }
    %>
  <% end %>

  <% if field["field_type"] == "time_zone" %>
      <%= select_tag field_name,
          options_for_select(ActiveSupport::TimeZone::MAPPING.values.uniq, field_value || ActiveSupport::TimeZone::MAPPING[current_user.tz]),
          {
            class: field_input_classes,
            include_blank: 'Please select...',
            required: !field['optional'].present?
          }
      %>
  <% end %>

  <% if field["field_type"] == "time" %>
    <input type="time" class="<%= field_input_classes %>" name="<%= field_name %>" value="<%= field_value %>" step="<%= field['step'] || 60 %>" <%= !field['optional'] && 'required' %>>
  <% end %>

  <% if field["field_type"] == "copyable" %>
    <%# TODO: remove use of send() given users have access to this via custom_fields; otherwise destructive action could be possible %>
    <% copyable_value = field["value"] || field_value || (field["value_method"] ? model.send(field["value_method"]) : '') %>

    <div data-controller="clipboard" data-clipboard-success-content-value="Copied!" class="mt-1 flex rounded-md shadow-sm">
      <input type="text" value="<%= copyable_value %>" data-clipboard-target="source" readonly="" class="col--span-6 bg-gray-50 border border-gray-300 text-gray-500 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500">
      <button type="button" data-action="clipboard#copy" data-clipboard-target="button" class="inline-flex items-center px-2 focus:outline-none rounded-r-xl border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm whitespace-nowrap">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-1 w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75"></path>
        </svg>
        Copy
      </button>
    </div>
  <% end %>

  <% if field["field_type"] == "copyable_webhook_url" %>
    <div data-controller="clipboard" data-clipboard-success-content-value="Copied!" class="mt-1 flex rounded-md shadow-sm">
      <input type="text" value="<%= model.webhook_url %>" data-clipboard-target="source" readonly="" class="col--span-6 bg-gray-50 border border-gray-300 text-gray-500 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-800 dark:border-gray-600 dark:placeholder-gray-400 dark:text-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500">
      <button type="button" data-action="clipboard#copy" data-clipboard-target="button" class="inline-flex items-center px-2 focus:outline-none rounded-r-xl border border-l-0 border-gray-300 bg-gray-50 text-gray-500 text-sm whitespace-nowrap">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="mr-1 w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75"></path>
        </svg>
        Copy
      </button>
    </div>
  <% end %>

  <% if field["field_type"] == "author_bio" %>

    <div class="mt-1">
      <% if field['github_url'].present? %>
        <%= link_to field['github_url'], target: :_blank do %>
          <%= image_tag 'github.svg', class: "dark:hidden w-5 inline" %>
          <%= image_tag 'github.svg', class: "hidden dark:inline w-5 inline", style: "filter: invert(1); -webkit-filter: invert(1)" %>
        <% end %>
      <% end %>

      <% if field['learn_more_url'].present? %>
        <%= link_to field['learn_more_url'], target: :_blank do %>
          <%= image_tag 'globe.svg', class: "dark:hidden w-5 inline" %>
          <%= image_tag 'globe.svg', class: "hidden dark:inline w-5 inline", style: "filter: invert(1); -webkit-filter: invert(1)" %>
        <% end %>
      <% end %>

      <% if field['email_address'].present? %>
        <%= mail_to field['email_address'], target: :_blank do %>
          <%= image_tag 'envelope.svg', class: "dark:hidden w-5 inline" %>
          <%= image_tag 'envelope.svg', class: "hidden dark:inline w-5 inline", style: "filter: invert(1); -webkit-filter: invert(1)" %>
        <% end %>
      <% end %>
    </div>
  <% end %>

  <% if field_is_previewable?(field) %>
    <%= render "plugin_settings/field_preview", field: field %>
  <% end %>

  <% if field['help_text'].present? %>
    <small class="<%= field_help_text_classes %>"><%= sanitize field['help_text'], form_field_sanitizers %></small>
  <% end %>
</div>
