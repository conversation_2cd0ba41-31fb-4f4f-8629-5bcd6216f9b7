<%= form_for @plugin_setting, :html => { class: layout_wide_classes } do |form| %>
  <%= form.hidden_field :id %>
  <%= form.hidden_field :code, value: params[:code] if params[:code].present? %>
  <div class="<%= layout_title_classes %>">
    <div class="my-4">
      <% if current_user.plugin_settings.where(plugin_id: @plugin.id).count.positive? %>
        <%= link_to plugin_settings_path(keyname: @plugin.keyname), class: "#{button_small_classes} #{button_secondary_classes}" do %>
          <%= render 'shared/icons/arrow_left' %>
          <%= t('.back_to_plugin', plugin: @plugin.name) %>
        <% end %>
      <% else %>
        <%= link_to plugins_path, class: "#{button_small_classes} #{button_secondary_classes}" do %>
          <%= render 'shared/icons/arrow_left' %>
          <%= t('.back_to_plugins') %>
        <% end %>
      <% end %>
    </div>

    <div class="w-full">
      <div class="flex mb-4">
        <div class="flex flex-grow items-center">
          <div class="flex justify-center mr-2 md:mr-4">
            <%= render partial: 'plugins/image', locals: { plugin: @plugin, plugin_setting: @plugin_setting } %>
          </div>
          <div class="flex flex-col">
            <h2 class="<%= title_h2_classes %>">
              <% if current_page?(new_plugin_setting_path) %>
                <%= @plugin.name %>
              <% else %>
                <%= @plugin_setting.name %>
              <% end %>
            </h2>
            <p class="<%= title_p_classes %>"><%= @plugin.description %></p>
          </div>
        </div>
        <div class="flex justify-end items-end shrink-0 gap-3">
          <% if @plugin_setting.persisted? && @plugin.datable? %>
            <%= link_to t('markups.form.edit_markup'), 'https://docs.usetrmnl.com/go/private-api/fetch-plugin-content', target: :_blank, class: "#{button_regular_classes} #{button_secondary_classes}" %>
          <% end %>

          <% if @plugin.private? && !@plugin_setting.read_only? %>
            <% if @plugin_setting.persisted? %>
              <%= link_to t('export'), export_plugin_setting_path(@plugin_setting), class: "#{button_regular_classes} #{button_secondary_classes}", data: { turbo: false }  %>
            <% end %>
            <%= render partial: "plugin_settings/edit_markup_button" %>
          <% end %>

          <% save_btn_classes = oauth_authorization_needed?(@plugin, @plugin_setting) ? button_disabled_classes : button_primary_classes %>
          <%= form.button t(:save), class: "#{button_regular_classes} #{save_btn_classes}", disabled: oauth_authorization_needed?(@plugin, @plugin_setting), data: { turbo: false } %>
        </div>
      </div>
    </div>
  </div>

  <div class="<%= layout_double_col_classes %>">
    <div class=<%= layout_meta_classes %>>
      <% if @plugin_setting.persisted? %>
        <%= render partial: 'health_status_banner', locals: { model: @plugin_setting } %>
      <% end %>
      <div class="<%= card_classes %> w-full relative mb-4">
        <%= turbo_stream_from current_user.id, @plugin_setting.id %>
        <%= plugin_preview_image_tag %>

        <% if screen_available? %>
          <% if @plugin.global? %>
            <span data-tooltip-target="tooltip-default" class="<%= badge_green_classes %> absolute top-4 right-4 cursor-pointer"><%= t('.refreshed') %>: <%= time_ago_in_words(most_recent_screen.updated_at) %> ago</span>
          <% elsif @plugin_setting.erroring? %>
            <span data-tooltip-target="tooltip-default" class="<%= badge_danger_classes %> absolute top-4 right-4 cursor-pointer"><%= t('.refresh_stopped') %></span>
            <div id="tooltip-default" role="tooltip" class="absolute z-20 invisible inline-block px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 tooltip">
              <%= @plugin_setting.error_message %>
              <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
          <% elsif @plugin_setting.playlists_count.zero? && @plugin_setting.mashup_contents_count.zero? %>
            <span data-tooltip-target="tooltip-default" class="<%= badge_gray_classes %> absolute top-4 right-4 cursor-pointer"><%= t('.refresh_paused') %></span>
            <div id="tooltip-default" role="tooltip" class="absolute z-20 invisible inline-block px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 tooltip">
              <%= t('.refresh_paused_hint') %>
              <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
          <% elsif @plugin_setting.playlists_count.zero? && @plugin_setting.mashup_contents_count.positive? %>
            <span data-tooltip-target="tooltip-default" class="<%= badge_gray_classes %> absolute top-4 right-4 cursor-pointer"><%= t('.refresh_paused_mashup') %></span>
            <div id="tooltip-default" role="tooltip" class="absolute z-20 invisible inline-block px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 tooltip">
              <%= t('.refresh_paused_mashup_hint') %>
              <br />
              <%= t('.synced') %>: <%= time_ago_in_words(@plugin_setting&.previous_refresh_at || DateTime.now) %> ago
              <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
          <% else %>
            <span data-tooltip-target="tooltip-default" class="<%= badge_green_classes %> absolute top-4 right-4 cursor-pointer"><%= t('.synced') %>: <%= time_ago_in_words(@plugin_setting&.previous_refresh_at || DateTime.now) %> ago</span>
            <div id="tooltip-default" role="tooltip" class="absolute z-20 invisible inline-block px-3 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 tooltip">
              <%= t('.refreshed') %>: <%= time_ago_in_words(most_recent_screen.updated_at) %> ago
              <div class="tooltip-arrow" data-popper-arrow></div>
            </div>
          <% end %>
        <% else %>
          <span class="<%= badge_blue_classes %> absolute top-4 right-4"><%= t('.preview') %></span>
        <% end %>
      </div>

      <%= render 'plugin_settings/thumbnails' %>

      <% if @plugin.knowledge_base_url && !@plugin_setting.recipe? && !@plugin_setting.fork? %>
        <div class="<%= message_classes %>">
          <div class="flex items-center w-full">
            <div class="flex-shrink-0 items-center">
              <%= render 'shared/icons/info' %>
            </div>
            <div class="w-full flex items-center justify-between ml-3">
              <p class="leading-none text-sm font-medium">
                <span class="sm:hidden"><%= t(:knowledge_base) %></span>
                <span class="hidden sm:inline"><%= t('.learn_more') %></span>
              </p>
              <p class="text-sm md:mt-0 md:ml-6">
              <a href="<%= @plugin.knowledge_base_url %>" target="_blank"
                 class="<%= button_small_classes %> <%= button_blue_classes %>"><%= t('.visit_docs') %>
                <%= render 'shared/icons/arrow_right' %>
              </a>
              </p>
            </div>
          </div>
        </div>
      <% end %>

      <% if @plugin_setting.fork? %>
        <%= form.hidden_field :upstream_id, value: @plugin_setting.upstream_id %>
        <div class="<%= message_classes %>">
          <div class="flex items-center w-full">
            <div class="flex-shrink-0 items-center">
              <%= render 'shared/icons/info' %>
            </div>
            <div class="w-full flex items-center justify-between ml-3">
              <p class="leading-none text-sm font-medium">
                <span class="sm:hidden"><%= t('.learn_more_recipe_short') %></span>
                <span class="hidden sm:inline"><%= t('.learn_more_recipe_long') %></span>
              </p>
              <p class="text-sm md:mt-0 md:ml-6">
                <a href="https://help.usetrmnl.com/en/articles/10122094-plugin-recipes" target="_blank"
                   class="<%= button_small_classes %> <%= button_blue_classes %>"><%= t(:learn_more) %>
                  <%= render 'shared/icons/arrow_right' %>
                </a>
              </p>
            </div>
          </div>
        </div>

        <div class="<%= message_classes %>">
          <div class="flex items-center w-full">
            <div class="flex-shrink-0 items-center">
              <%= render 'shared/icons/fork', classes: 'h-5 w-5' %>
            </div>
            <div class="w-full flex items-center justify-between ml-3">
              <p class="leading-none text-sm font-medium">
                <span class="inline"><%= t('.learn_more_fork') %></span>
              </p>
              <p class="text-sm md:mt-0 md:ml-6">
              <%= link_to recipe_path(@plugin_setting.upstream_id), target: '_blank', class: "#{button_small_classes} #{button_blue_classes}" do %>
                <%= t('.learn_more_fork_original') %>
                <%= render 'shared/icons/arrow_right' %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>

      <% if @plugin_setting.fork? && @plugin_setting.recipe_author.present? %>
        <div class="<%= message_classes %>">
          <div class="flex items-center w-full">
            <div class="flex-shrink-0 items-center">
              <%= render 'shared/icons/support', classes: 'h-5 w-5' %>
            </div>
            <div class="w-full flex items-center justify-between ml-3">
              <p class="leading-none text-sm font-medium">
                <span class="inline"><%= t('.learn_more_recipe_author', recipe_author: @plugin_setting.recipe_author) %></span>
              </p>
            </div>
          </div>
        </div>
      <% end %>

      <% if @plugin_setting.custom_fields&.any?  %>
        <% visibility_rules = conditional_visibility_rules(@plugin_setting.custom_fields) %>
        <div id="custom_fields_container" class="<%= card_classes %> <%= card_divide_classes %> mb-6" 
             data-controller="custom-fields<%= ' conditional-visibility' if visibility_rules.any? %>"
             <%= visibility_rules.any? ? "data-conditional-visibility-rules-value='#{j visibility_rules.to_json}'".html_safe : '' %>>

          <% @plugin_setting.custom_fields.each do |account_field| %>
            <%= render partial: 'plugin_settings/field', locals: { model: @plugin_setting, field: account_field, attr_namespace: '[custom_fields_values]' } %>
          <% end %>
        </div>
      <% end %>

      <% if @plugin_setting.read_only_fork? %>
        <% visibility_rules = conditional_visibility_rules(@plugin_setting.upstream.custom_fields) %>
        <div id="custom_fields_container" class="<%= card_classes %> <%= card_divide_classes %> mb-6" 
             data-controller="custom-fields<%= ' conditional-visibility' if visibility_rules.any? %>"
             <%= visibility_rules.any? ? "data-conditional-visibility-rules-value='#{j visibility_rules.to_json}'".html_safe : '' %>>

          <% @plugin_setting.upstream.custom_fields.each do |account_field| %>
            <%= render partial: 'plugin_settings/field', locals: { model: @plugin_setting, field: account_field, attr_namespace: '[custom_fields_values]' } %>
          <% end %>
        </div>
      <% end %>

      <%# Readonly recipe doesn't need access to account_fields %>
      <% if @plugin_setting.read_only_fork? %>
        <%= hidden_field_tag 'plugin_setting[settings][read_only]', true %>
      <% end %>

      <%# put default settings inside accordion ONLY IF plugin is private and custom_fields exist %>
      <%# default OPEN the accordion IF the plugin_setting is a Recipe Fork - end users likely don't need access %>
      <% if @plugin.account_fields&.any? && @plugin_setting.custom_fields&.any? && !@plugin_setting.read_only_fork? %>
        <% settings_accordion_expanded = !@plugin_setting.fork? %>

        <div id="accordion-open" data-accordion="open">
          <h2 id="accordion-open-heading-1">
            <button type="button" class="flex items-center justify-between w-full py-2 px-4 font-medium rtl:text-right text-gray-500 border border-gray-200 rounded-lg [&[aria-expanded=true]]:rounded-b-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-800 dark:border-gray-700 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 gap-3 mb-2" data-accordion-target="#accordion-open-body-1" aria-expanded="<%= settings_accordion_expanded %>" aria-controls="accordion-open-body-1">
              <span class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="size-5" width="32" height="32" fill="currentColor" viewBox="0 0 256 256"><path d="M54.8,119.49A35.06,35.06,0,0,1,49.05,128a35.06,35.06,0,0,1,5.75,8.51C60,147.24,60,159.83,60,172c0,25.94,1.84,32,20,32a12,12,0,0,1,0,24c-19.14,0-32.2-6.9-38.8-20.51C36,196.76,36,184.17,36,172c0-25.94-1.84-32-20-32a12,12,0,0,1,0-24c18.16,0,20-6.06,20-32,0-12.17,0-24.76,5.2-35.49C47.8,34.9,60.86,28,80,28a12,12,0,0,1,0,24c-18.16,0-20,6.06-20,32C60,96.17,60,108.76,54.8,119.49ZM240,116c-18.16,0-20-6.06-20-32,0-12.17,0-24.76-5.2-35.49C208.2,34.9,195.14,28,176,28a12,12,0,0,0,0,24c18.16,0,20,6.06,20,32,0,12.17,0,24.76,5.2,35.49A35.06,35.06,0,0,0,207,128a35.06,35.06,0,0,0-5.75,8.51C196,147.24,196,159.83,196,172c0,25.94-1.84,32-20,32a12,12,0,0,0,0,24c19.14,0,32.2-6.9,38.8-20.51C220,196.76,220,184.17,220,172c0-25.94,1.84-32,20-32a12,12,0,0,0,0-24Z"></path></svg>
                <%= t('.advanced_settings') %>
              </span>
              <svg data-accordion-icon class="w-3 h-3 rotate-180 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5 5 1 1 5"/>
              </svg>
            </button>
          </h2>
          <div id="accordion-open-body-1" class="hidden" aria-labelledby="accordion-open-heading-1">
            <% visibility_rules = conditional_visibility_rules(@plugin.account_fields) %>
            <div class="<%= card_classes %> <%= card_divide_classes %> mb-6"
                 <%= visibility_rules.any? ? raw('data-controller="conditional-visibility"') : '' %>
                 <%= visibility_rules.any? ? raw("data-conditional-visibility-rules-value=\"#{visibility_rules.to_json.gsub('"', '&quot;')}\"") : '' %>>
              <% @plugin.account_fields.each do |account_field| %>
                <%= render partial: 'plugin_settings/field', locals: { model: @plugin_setting, field: account_field } %>
              <% end %>
            </div>
          </div>
        </div>
      <% elsif !@plugin_setting.read_only_fork? %>
        <%# native plugins don't have custom fields, they are all account_fields, so no accordion necessary %>
        <% visibility_rules = conditional_visibility_rules(@plugin.account_fields) %>
        <div class="<%= card_classes %> <%= card_divide_classes %> mb-6"
             <%= visibility_rules.any? ? raw('data-controller="conditional-visibility"') : '' %>
             <%= visibility_rules.any? ? raw("data-conditional-visibility-rules-value=\"#{visibility_rules.to_json.gsub('"', '&quot;')}\"") : '' %>>
          <% @plugin.account_fields.each do |account_field| %>
            <%= render partial: 'plugin_settings/field', locals: { model: @plugin_setting, field: account_field } %>
          <% end %>
        </div>
      <% end %>
    </div>

    <div class="<%= layout_plugin_settings_classes %>">
      <%= form.hidden_field :plugin_id, value: @plugin.id %>

      <% if @plugin.oauth2? || @plugin.third_party? %>
        <div class="<%= card_classes %> p-6 mb-6">
          <% if oauth_authorization_needed?(@plugin, @plugin_setting) %>
            <label class="<%= field_label_classes %>"><%= t('.link_oauth') %></label>
            <p class="<%= field_description_classes %>"><%= t('.link_oauth_hint', plugin: @plugin.name) %></p>
            <%= link_to t('.connect_with', plugin: @plugin.name),
                        @plugin.redirect_url(current_user.id),
                        class: "#{button_regular_classes} #{button_primary_classes} text-xs mt-4"
            %>
          <% elsif @plugin.third_party? && @plugin_setting.persisted? %>
            <%= link_to t('.manage_plugin', plugin: @plugin.name),
                        configure_plugin_setting_path(@plugin_setting),
                        class: "#{button_regular_classes} #{button_primary_classes} mt-4",
                        target: '_blank'
            %>
          <% else %>
            <label class="<%= field_label_classes %>"><%= t('.linked_account') %></label>
            <p class="<%= field_description_classes %>">
              <%= t('.linked_account_success', plugin_name: @plugin.name) %>
            </p>
            <p class="<%= field_description_classes %>">
              <%# if OAuth was just connected, a plugin_setting does not yet exist, credentials live on User record; otherwise they live on plugin_setting record %>
              <%= link_to t('.disconnect_account'), reset_credentials_plugin_setting_path(keyname: @plugin.keyname, id: @plugin_setting&.id || @plugin.keyname), data: { turbo_method: :post, turbo_confirm: 'Are you sure?' }, class: 'underline' %>
            </p>
          <% end %>
        </div>
      <% end %>

      <div class="<%= card_classes %> <%= card_divide_classes %> mb-6">
        <div class="p-6">
          <label for="plugin_setting_name" class="<%= field_label_classes %>">
            <%= t('.name') %>
          </label>
          <p class="<%= field_description_classes %> mb-0">
          <% if !@plugin.global? %>
            <%= t('.multiple_instances_hint') %>
          <% end %>
          <%= t('.name_hint') %>
          </p>
          <%= form.text_field :name, required: true, class: field_input_classes, placeholder: "#{@plugin.name_placeholder}", value: form.object.name || @plugin.name_placeholder %>
        </div>

        <% unless @plugin.hide_refresh_interval? || current_page?(new_plugin_setting_path) %>
          <div class="p-6">
            <label for="plugin_setting_refresh_interval" class="<%= field_label_classes %>">
              <%= t('.refresh_rate') %>
            </label>
            <p class="<%= field_description_classes %>"><%= t('.refresh_rate_hint') %></p>
            <%= form.select :refresh_interval, options_for_select(@plugin_setting.refresh_options, selected: @plugin_setting.refresh_interval), {}, { class: field_input_classes } %>
            <small class="<%= field_help_text_classes %>">
              <%= string_with_link(t(:refresh_rates_hint), 'https://help.usetrmnl.com/en/articles/10113695-how-refresh-rates-work', target: :_blank, class: 'underline') %>
            </small>
          </div>
        <% end %>

        <% if @plugin_setting.persisted? %>
          <% if @plugin.private? && !@plugin_setting.read_only_fork? %>
            <div class="p-6">
              <label for="plugin_setting_uuid" class="<%= field_label_classes %>">
                <%= t('.plugin_uuid') %>
              </label>
              <p class="<%= field_description_classes %>"><%= t('.plugin_uuid_hint') %></p>
              <%= form.text_field :uuid, readonly: true, required: true, class: field_input_classes %>
            </div>
          <% end %>

          <% unless @plugin.global? %>
            <div class="p-6">
              <label class="<%= field_label_classes %>">
                <%= t('.force_refresh') %>
              </label>
              <% if @plugin_setting.renders_screen? %>
                <p id="force_refresh" class="<%= field_description_classes %>">
                  <%= link_to t('.force_refresh_click_here'), force_refresh_plugin_setting_path(@plugin_setting.uuid), data: { turbo_method: :post, turbo_confirm: 'Are you sure?' }, class: 'underline inline' %>
                  <%= t('.force_refresh_click_here_hint') %>.
                </p>
                <small class="<%= field_help_text_classes %>">
                  <%= t('.force_refresh_hint') %>
                  <%= string_with_link(t(:refresh_rates_hint), 'https://help.usetrmnl.com/en/articles/10113695-how-refresh-rates-work', target: :_blank, class: 'underline') %>
                </small>
              <% else %>
                <p class="<%= field_description_classes %>">This plugin cannot be force-refreshed because the TRMNL server does not perform image processing. Learn how to test your instance <%= link_to 'here', 'https://help.usetrmnl.com/en/articles/11628971-testing-your-alias-or-redirect-plugin', target: :_blank, class: 'underline' %>.</p>
              <% end %>
            </div>

            <% if @plugin_setting.can_debug_logs? && !@plugin_setting.read_only_fork? %>
              <div class="p-6">
                <label class="<%= field_label_classes %>">
                  <%= t('.debug_logs') %>
                </label>
                <p id="debug_logs" class="<%= field_description_classes %>">
                  <%= render 'debug_logs_status' %>
                </p>
              </div>
            <% end %>
          <% end %>
        <% end %>
      </div>

      <% if @plugin_setting.persisted? %>
        <% if @plugin.private? && !@plugin_setting.read_only_fork? %>
          <div class="flex items-center <%= card_classes %> p-6 min-h-[86px] text-black dark:text-white mb-6">
            <div class="flex items-center w-full" data-controller="icon-preview">
              <div class="w-full flex items-center justify-between">
                <div class="mr-4">
                  <label class="<%= field_label_classes %>">
                    Icon [Beta]
                  </label>
                  <small class="text-gray-500 dark:text-gray-400">PNG; <%= t('plugins.new.icon_hint') %></small>
                </div>

                <%= form.file_field :icon, direct_upload: true, class: 'hidden', accept: '.png', data: { 'icon-preview-target': 'icon', action: 'change->icon-preview#previewIcon' } %>
                <%= image_tag @plugin_setting.custom_icon, id: 'icon_preview', data: { action: 'click->icon-preview#openFilePicker' }, class: "cursor-pointer h-10 w-10 min-h-10 min-w-10 max-h-10 max-w-10 object-cover object-center" %>
              </div>
            </div>
          </div>

          <div class="flex items-center <%= card_classes %> p-6 min-h-[86px] text-black dark:text-white mb-6">
            <div class="flex items-center w-full">
              <div id="recipe_submit" class="w-full flex items-center justify-between">
                <div class="mr-4">
                  <label class="<%= field_label_classes %>">
                    <%= recipe_cta_heading(@plugin_setting) %>
                  </label>
                  <p class="<%= field_description_classes %> mb-0">
                    <%= recipe_cta_body(@plugin_setting) %>
                  </p>
                </div>

                <%= recipe_cta_btn(@plugin_setting) %>
              </div>
            </div>
          </div>
        <% end %>

        <% unless @plugin_setting.recipe? %>
          <div class="flex items-center <%= card_classes %> p-6 min-h-[86px] text-black dark:text-white mb-6">
            <div class="flex items-center w-full">
              <div class="w-full flex items-center justify-between">
                <div class="mr-4">
                  <label class="<%= field_label_classes %>">
                    <%= t('.remove_plugin') %>
                  </label>
                  <p class="<%= field_description_classes %> mb-0">
                    <%= t('.remove_plugin_hint') %>
                  </p>
                </div>

                <%= link_to plugin_setting_path(@plugin_setting.id), data: { turbo_confirm: t('plugin_settings.delete'), turbo_method: :delete }, class: "#{button_regular_classes} #{button_secondary_classes} !p-2 mb-2 ml-4" do %>
                  <%= render 'shared/icons/delete' %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>

    </div>
  </div>
<% end %>

<% if reinit_required?(@plugin.keyname) %>
  <script>
    localStorage.setItem('<%= @plugin.keyname %>', '<%= @plugin.token(current_user.id) %>')
  </script>
<% end %>

<script>
  document.addEventListener('turbo:load', function() {
    var xhrFetchNode = document.querySelectorAll(".xhr-fetch");
    xhrFetchNode.forEach((xhrFetch) => {
      if (xhrFetch) {
        document.querySelectorAll("input, select").forEach(input => {
          input.addEventListener('change', (event) => {
            fetchData(xhrFetch);
          })
        })
        fetchData(xhrFetch);
      }
    })

    function fetchData(xhrFetch) {
      let renderStrategy = xhrFetch.dataset.renderStrategy;
      let endpoint = xhrFetch.dataset.endpoint;
      let remote = xhrFetch.dataset.remote;
      let params = {
        function: xhrFetch.dataset.function,
        plugin_setting: {}
      };
      document.querySelectorAll("input[type='text'], select, input[type='hidden']").forEach(textfield => {
        let name = textfield.name.replace(/plugin_setting/, '');
        name = name.replace(/\]\[/gi, '_').replace(/\[|\]/gi,'');
        params['plugin_setting'][name] = textfield.value;
      });

      // don't share private / unnecessary context in requests to remotely located xhrFetch content
      if (remote == 'true') {
        // webhook URL == ''; doesn't have a name/ID because it should be ignored by form submit
        let keysToDelete = ['_method', 'authenticity_token', '', 'uuid', 'plugin_id'];
        keysToDelete.forEach(key => delete params.plugin_setting[key]);
      }

      $.ajax({
        url: endpoint,
        method: "POST",
        beforeSend: function(xhr) {xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrf-token"]').attr('content'))},
        headers: {
          'content-type': 'application/json'
        },
        data: JSON.stringify(params),
        success: function(data) {
          // dynamically populate 'select' dropdowns with server data
          if (renderStrategy == 'select') {
            let pluginSetting = <%== @plugin_setting.all_settings.to_json %>;
            let selectedOptions;

            if (xhrFetch.dataset.remote == 'true') {
              customFieldsValues = pluginSetting.custom_fields_values || {}
              selectedOptions = customFieldsValues[params.function] || []
            } else {
              let accountFields = <%== @plugin.account_fields.to_json %>;
              let accountField = accountFields.find(f => {
                return (xhrFetch.name.includes(f.keyname))
              });
              selectedOptions = (pluginSetting[accountField.keyname] || []);
            }

            // handle data formatting errors (json response is null or a string)
            if (data === null) { data = [{ 'Not Found': 'not_found' }] }
            for (var i = 0; i < data.length; i++){
              let option = document.createElement('option');
              option.textContent = Object.keys(data[i]);
              option.value = Object.values(data[i]);

              let insertStrategy = 'appendChild';

              selectedOptsArray = typeof(selectedOptions) == 'string' ? [selectedOptions] : Array.from(selectedOptions);
              selectedOptsArray.forEach(function(selectedOption) {
                if (selectedOption == option.value) {
                  insertStrategy = 'prepend';
                  option.selected = true;
                }
              })

              if (xhrFetch.length <= data.length) {
                xhrFetch[insertStrategy](option);
              }
            }
          }
          // dynamically populate 'text' inputs with server data
          if (renderStrategy == 'string') {
            xhrFetch.value = data.content;
          }
        },
        // handle endpoint response errors (POST request fails)
        error: function(data) {
          $(xhrFetch).empty(); // remove existing options to prevent duplicates
          let option = document.createElement('option');
          option.textContent = 'Not Found';
          option.value = 'not_found';
          xhrFetch.prepend(option);
        }
      })
    }
  });

  // URL Preview functionality
  function previewField(fieldKey, pluginSettingId) {
    const form = document.querySelector('form');
    const formData = new FormData(form);
    const fieldInput = document.getElementById(fieldKey + '_input');
    const fieldTemplate = fieldInput.value;

    if (!fieldTemplate.trim()) {
      alert('Please enter content to preview');
      return;
    }

    // Add the field template and field key to form data
    formData.append('field_template', fieldTemplate);
    formData.append('field_key', fieldKey);

    // Show loading state
    const previewDiv = document.getElementById('field_preview_' + fieldKey);
    const contentDiv = document.getElementById('field_preview_content_' + fieldKey);
    previewDiv.classList.remove('hidden');
    contentDiv.textContent = 'Loading...';

    fetch('/plugin_settings/' + pluginSettingId + '/preview_field', {
      method: 'POST',
      headers: {
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: formData
    })
    .then(response => {
      return response.text().then(text => {
        if (response.ok) {
          try {
            return JSON.parse(text);
          } catch (e) {
            throw new Error('Invalid JSON response: ' + text.substring(0, 100));
          }
        } else {
          throw new Error('HTTP ' + response.status + ': ' + text);
        }
      });
    })
    .then(data => {
      if (data.success) {
        contentDiv.className = contentDiv.className.replace('text-red-600', 'text-gray-900');
        contentDiv.textContent = data.preview_content;
      } else {
        contentDiv.textContent = 'Error: ' + (data.error || 'Unknown error');
        contentDiv.className = contentDiv.className.replace('text-gray-900', 'text-red-600');
      }
    })
    .catch(error => {
      contentDiv.textContent = 'Error: ' + error.message;
      contentDiv.className = contentDiv.className.replace('text-gray-900', 'text-red-600');
    });
  }

</script>
