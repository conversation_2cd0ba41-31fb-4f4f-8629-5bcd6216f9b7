<%
  # Default to system theme for framework, but allow it to be overridden
  theme ||= "system"

  # For system theme, we'll use CSS classes that adapt to the user's system preference
  if theme == "system"
    # Define system-aware classes with both light and dark variants
    text_color = "text-black dark:text-white"
    hover_bg = "hover:bg-gray-100 dark:hover:bg-gray-800"
    hover_text = "hover:text-black dark:hover:text-white"
    active_border = "border-black dark:border-white"
  else
    # Define text and hover classes based on explicit theme
    text_color = theme == "dark" ? "text-white" : "text-black"
    hover_bg = theme == "dark" ? "hover:bg-gray-800" : "hover:bg-gray-100"
    hover_text = theme == "dark" ? "hover:text-white" : "hover:text-black"
    # Active link underline color
    active_border = theme == "dark" ? "border-white" : "border-black"
  end

  # Logo based on theme
  if theme == "system"
    # For system theme, we'll dynamically switch the logo based on the system preference
    logo_light = "trmnl--glyph-black.svg"
    logo_dark = "trmnl--glyph-white.svg"
    logo_class = "h-8 mt-1 block dark:hidden"
    logo_dark_class = "h-8 mt-1 hidden dark:block"
  else
    logo = theme == "dark" ? "trmnl--glyph-white.svg" : "trmnl--glyph-black.svg"
  end

  # Helper method to create navigation links with proper classes
  def nav_link(text, path, theme_text_color, theme_hover_bg, theme_hover_text)
    base_class = "inline-block py-2 px-3 transition-all duration-200 text-sm font-medium tracking-tight rounded-full"

    # Check if current path starts with the given path (handles child routes)
    is_active = request.path.start_with?(path) && path != "/" # Special case for root path
    # For root path, we only want it active when it's exactly "/"
    is_active = current_page?(path) if path == "/"

    if is_active
      # Active class with line indicator that disappears on hover using pure Tailwind
      line_class = "before:content-[''] before:absolute before:h-0.5 before:bg-primary-500 before:left-3 before:right-3 before:bottom-0 before:opacity-100 hover:before:opacity-0 before:transition-opacity"
      link_to text, path, class: "#{base_class} #{theme_hover_bg} #{theme_hover_text} #{theme_text_color} relative #{line_class}"
    else
      link_to text, path, class: "#{base_class} #{theme_hover_bg} #{theme_hover_text} #{theme_text_color}"
    end
  end
%>

<div class="fixed top-0 left-0 right-0 w-full z-50 <%= theme == 'dark' ? 'bg-gray-900/85' : 'bg-gray-100/85' %> <%= theme == 'system' ? 'bg-gray-100/85 dark:bg-gray-900/85' : '' %> backdrop-blur-md shadow-sm">
  <div class="w-full max-w-9xl px-4 mx-auto">
    <nav class="relative py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <a href="/framework_v2" class="inline-block flex items-center ml-1">
            <% if theme == "system" %>
              <%= image_tag logo_light, class: logo_class %>
              <%= image_tag logo_dark, class: logo_dark_class %>
            <% else %>
              <%= image_tag logo, class: "h-8 #{text_color} mt-1" %>
            <% end %>
            <span class="ml-2 <%= text_color %> font-medium">Framework</span>
          </a>
        </div>

        <!-- Mobile menu button -->
        <button id="framework-mobile-menu-button" class="md:hidden p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 <%= text_color %>" aria-expanded="false">
          <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <!-- Desktop navigation -->
        <div class="hidden md:flex items-center gap-2">
          <% unless params[:controller] == 'framework_legacy' %>
            <!-- Group 1: Theme and Orientation toggles -->
            <!-- Dark mode toggle -->
            <button id="dark-mode-toggle" 
                    class="inline-block p-2 transition-all duration-200 text-sm font-medium tracking-tight rounded-full <%= hover_bg %> <%= text_color %> bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 relative"
                    aria-label="Toggle dark mode">
              <%= render 'shared/icons/light_mode', css_class: "w-5 h-5", element_id: "light-icon" %>
              <%= render 'shared/icons/dark_mode', css_class: "w-5 h-5", element_id: "dark-icon", additional_classes: "hidden" %>
              <!-- Dark mode indicator dot -->
              <div id="dark-mode-indicator" class="absolute top-1 right-1 w-1.5 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full hidden"></div>
            </button>
            
            <!-- Orientation toggle -->
            <button id="orientation-toggle" 
                    class="inline-block p-2 transition-all duration-200 text-sm font-medium tracking-tight rounded-full <%= hover_bg %> <%= text_color %> bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 relative"
                    aria-label="Toggle orientation">
              <%= render 'shared/icons/landscape', css_class: "w-5 h-5", element_id: "landscape-icon" %>
              <%= render 'shared/icons/portrait', css_class: "w-5 h-5", element_id: "portrait-icon", additional_classes: "hidden" %>
              <!-- Orientation indicator dot -->
              <div id="orientation-indicator" class="absolute top-1 right-1 w-1.5 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full hidden"></div>
            </button>
            
            <div class="w-px bg-gray-300 dark:bg-gray-650 self-stretch"></div>
            
            <!-- Group 2: Device controls -->
            <!-- Device selector dropdown -->
            <%= select_tag 'device_selector', 
                          options_for_select(framework_device_options, 'og_png'),
                          { 
                            id: 'device-selector',
                            class: "inline-block py-2 px-3 pr-8 transition-all duration-200 text-sm font-medium tracking-tight rounded-full #{hover_bg} #{text_color} bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500",
                            data: { should_refresh: device_selector_should_refresh? }
                          } %>
            
            <!-- Bit depth selector dropdown -->
            <%= select_tag 'bit_depth_selector', 
                          options_for_select([
                            ['1-bit', '1'],
                            ['2-bit', '2'],
                            ['4-bit', '4']
                          ], '1'),
                          { 
                            id: 'bit-depth-selector',
                            class: "inline-block py-2 px-3 pr-8 transition-all duration-200 text-sm font-medium tracking-tight rounded-full #{hover_bg} #{text_color} bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500"
                          } %>
            
            <!-- Reset to defaults button -->
            <button id="reset-device-defaults" 
                    class="inline-block p-2 transition-all duration-200 text-sm font-medium tracking-tight rounded-full <%= hover_bg %> <%= text_color %> bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500"
                    aria-label="Reset to device defaults"
                    title="Reset to device defaults">
              <%= render 'shared/icons/reset', css_class: "w-5 h-5" %>
            </button>
            
            <div class="w-px bg-gray-300 dark:bg-gray-650 self-stretch"></div>
          <% end %>

          <!-- Group 3: Navigation links -->
          <%= nav_link(t(:blog), blog_posts_path, text_color, hover_bg, hover_text) %>
          <%= nav_link("Developers", developers_path, text_color, hover_bg, hover_text) %>

          <% if current_user %>
            <%= nav_link(t('dashboard.index.title'), dashboard_index_path, text_color, hover_bg, hover_text) %>
            <%= nav_link(t(:log_out), logout_path, text_color, hover_bg, hover_text) %>
          <% else %>
            <%= nav_link(t(:login), new_user_session_path, text_color, hover_bg, hover_text) %>
            <%= nav_link(t(:sign_up), new_user_registration_path, text_color, hover_bg, hover_text) %>
            <a href="/" class="ml-2 inline-block py-2 px-4 bg-primary-500 hover:bg-primary-600 text-white font-medium rounded-full transition-colors duration-200 text-sm tracking-tight">
              Get TRMNL
            </a>
          <% end %>
        </div>
      </div>

      <!-- Mobile navigation menu -->
      <div id="framework-mobile-menu" class="md:hidden hidden fixed inset-0 z-[100] transform transition-all duration-300 ease-in-out opacity-0">
        <div class="h-full w-full min-h-screen <%= theme == 'dark' ? 'bg-gray-750' : 'bg-gray-250' %> <%= theme == 'system' ? 'bg-gray-250 dark:bg-gray-750' : '' %> pt-0 flex flex-col">
          <!-- Header with logo and close button -->
          <div class="absolute top-0 left-0 right-0 px-8 md:px-20 xl:px-32 max-w-9xl xs:pt-4 mx-auto w-full">
            <div class="relative py-4">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <a href="/" class="inline-block flex items-center">
                    <% if theme == "system" %>
                      <%= image_tag logo_light, class: logo_class %>
                      <%= image_tag logo_dark, class: logo_dark_class %>
                    <% else %>
                      <%= image_tag logo, class: "h-8 #{text_color} mt-1" %>
                    <% end %>
                    <span class="ml-2 <%= text_color %> font-medium">Framework Docs</span>
                  </a>
                </div>

                <button id="framework-mobile-menu-close" class="p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 <%= theme == 'dark' ? 'text-white' : 'text-black' %> <%= theme == 'system' ? 'text-black dark:text-white' : '' %>">
                  <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- Mobile navigation content - centered in the remaining space -->
          <div class="flex-1 flex items-center justify-center">
            <div class="flex flex-col items-center gap-6 px-4 text-lg">
              <% unless params[:controller] == 'framework_legacy' %>
                <!-- Group 1: Theme and Orientation toggles -->
                <!-- Dark mode toggle for mobile -->
                <button id="dark-mode-toggle-mobile" 
                        class="inline-block p-2 transition-all duration-200 text-sm font-medium tracking-tight rounded-full <%= hover_bg %> <%= text_color %> bg-transparent border-0 cursor-pointer relative"
                        aria-label="Toggle dark mode">
                  <div class="flex items-center gap-2">
                    <%= render 'shared/icons/light_mode', css_class: "w-5 h-5", element_id: "light-icon-mobile" %>
                    <%= render 'shared/icons/dark_mode', css_class: "w-5 h-5", element_id: "dark-icon-mobile", additional_classes: "hidden" %>
                    <span id="dark-mode-text-mobile">Light Mode</span>
                    <!-- Dark mode indicator dot for mobile -->
                    <div id="dark-mode-indicator-mobile" class="absolute top-1 right-1 w-1.5 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full hidden"></div>
                  </div>
                </button>
                
                <!-- Orientation toggle for mobile -->
                <button id="orientation-toggle-mobile" 
                        class="inline-block p-2 transition-all duration-200 text-sm font-medium tracking-tight rounded-full <%= hover_bg %> <%= text_color %> bg-transparent border-0 cursor-pointer relative"
                        aria-label="Toggle orientation">
                  <div class="flex items-center gap-2">
                    <%= render 'shared/icons/landscape', css_class: "w-5 h-5", element_id: "landscape-icon-mobile" %>
                    <%= render 'shared/icons/portrait', css_class: "w-5 h-5", element_id: "portrait-icon-mobile", additional_classes: "hidden" %>
                    <span id="orientation-text-mobile">Landscape</span>
                    <!-- Orientation indicator dot for mobile -->
                    <div id="orientation-indicator-mobile" class="absolute top-1 right-1 w-1.5 h-1.5 bg-primary-600 dark:bg-primary-400 rounded-full hidden"></div>
                  </div>
                </button>
                
                <div class="h-px bg-gray-300 dark:bg-gray-650 w-full"></div>

                <!-- Group 2: Device controls -->
                <!-- Device selector for mobile -->
                <%= select_tag 'device_selector_mobile', 
                              options_for_select(framework_device_options, 'og_png'),
                              { 
                                id: 'device-selector-mobile',
                                class: "inline-block py-2 px-3 pr-8 transition-all duration-200 text-sm font-medium tracking-tight rounded-full #{hover_bg} #{text_color} bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500",
                                data: { should_refresh: device_selector_should_refresh? }
                              } %>
                
                <!-- Bit depth selector for mobile -->
                <%= select_tag 'bit_depth_selector_mobile', 
                              options_for_select([
                                ['1-bit', '1'],
                                ['2-bit', '2'], 
                                ['4-bit', '4']
                              ], '1'),
                              { 
                                id: 'bit-depth-selector-mobile',
                                class: "inline-block py-2 px-3 pr-8 transition-all duration-200 text-sm font-medium tracking-tight rounded-full #{hover_bg} #{text_color} bg-transparent border-0 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500"
                              } %>
                
                <!-- Reset to defaults button for mobile -->
                <button id="reset-device-defaults-mobile" 
                        class="inline-block p-2 transition-all duration-200 text-sm font-medium tracking-tight rounded-full <%= hover_bg %> <%= text_color %> bg-transparent border-0 cursor-pointer relative"
                        aria-label="Reset to device defaults">
                  <div class="flex items-center gap-2">
                    <%= render 'shared/icons/reset', css_class: "w-5 h-5" %>
                    <span>Reset to defaults</span>
                  </div>
                </button>
                
                <div class="h-px bg-gray-300 dark:bg-gray-650 w-full"></div>
              <% end %>
              
              <!-- Group 3: Navigation links -->
              <%= nav_link(t(:blog), blog_posts_path, text_color, hover_bg, hover_text) %>
              <%= nav_link("Developers", developers_path, text_color, hover_bg, hover_text) %>

              <% if current_user %>
                <%= nav_link(t('dashboard.index.title'), dashboard_index_path, text_color, hover_bg, hover_text) %>
                <%= nav_link(t(:log_out), logout_path, text_color, hover_bg, hover_text) %>
              <% else %>
                <%= nav_link(t(:login), new_user_session_path, text_color, hover_bg, hover_text) %>
                <%= nav_link(t(:sign_up), new_user_registration_path, text_color, hover_bg, hover_text) %>
                <a href="/" class="mt-2 inline-block py-2 px-4 bg-primary-500 hover:bg-primary-600 text-white font-medium rounded-full transition-colors duration-200 text-sm tracking-tight">
                  Get TRMNL
                </a>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </div>
</div>

<% if theme == "system" %>
  <script>
    // Add the dark class to html when the system preference is dark
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.documentElement.classList.add('dark');
    }

    // Listen for changes in the system preference
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
      if (e.matches) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    });
  </script>
<% end %>

<script>
  // Mobile menu toggle for framework nav
  document.addEventListener('DOMContentLoaded', function() {
    initFrameworkMobileMenu();
  });

  // Re-initialize after turbo navigation
  document.addEventListener('turbo:load', function() {
    initFrameworkMobileMenu();
  });


  function initFrameworkMobileMenu() {
    const menuButton = document.getElementById('framework-mobile-menu-button');
    const mobileMenu = document.getElementById('framework-mobile-menu');
    const closeButton = document.getElementById('framework-mobile-menu-close');

    // Return early if elements don't exist
    if (!menuButton || !mobileMenu || !closeButton) return;

    // Remove any existing event listeners by cloning only the specific buttons
    // Don't clone the entire mobile menu as it contains toggle buttons with event listeners
    const newMenuButton = menuButton.cloneNode(true);
    const newCloseButton = closeButton.cloneNode(true);
    
    menuButton.parentNode.replaceChild(newMenuButton, menuButton);
    closeButton.parentNode.replaceChild(newCloseButton, closeButton);

    // Show mobile menu
    newMenuButton.addEventListener('click', function() {
      mobileMenu.classList.remove('hidden');
      // Force a reflow
      mobileMenu.offsetHeight;
      // Add visible classes
      mobileMenu.classList.add('opacity-100');
      mobileMenu.classList.remove('opacity-0');
      

    });

    // Hide mobile menu
    newCloseButton.addEventListener('click', function() {
      mobileMenu.classList.remove('opacity-100');
      mobileMenu.classList.add('opacity-0');
      // Wait for transition to complete before hiding
      setTimeout(() => {
        mobileMenu.classList.add('hidden');
      }, 300);
    });
  }
</script> 