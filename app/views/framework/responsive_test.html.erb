<%= stylesheet_link_tag "framework/responsive_test", "data-turbo-track": "reload" %>
<div class="<%= layout_framework_classes %>">
  <div class="<%= layout_framework_title_classes %>">
    <div class="flex items-center justify-between">
      <div>
        <%= render 'framework/page_header', page_name: :responsive_test %>
      </div>

    </div>
  </div>

  <div class="<%= layout_framework_content_classes %>">
    <p class="text-gray-600 dark:text-gray-400">
      This page tests responsive utilities by comparing SCSS mixins with CSS classes across different screen conditions.
      Each test row shows an element styled with SCSS mixins alongside the same element styled with CSS utility classes.
      Both columns should look identical when the conditions are met, demonstrating that mixins and classes produce equivalent results.
    </p>

    <div class="space-y-16">
      <section class="space-y-8">
        <%= framework_heading(3, 'Test Overview') %>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <%= framework_heading(4, 'Components Tested') %>
            <ul class="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Background (all 7 variants)</li>
              <li>• Visibility (all 7 variants)</li>
              <li>• Text (all 7 variants)</li>
              <li>• Value (all 7 variants)</li>
              <li>• Layout/Flexbox (size + orientation only)</li>
            </ul>
          </div>
          <div>
            <%= framework_heading(4, 'Variants Tested') %>
            <ul class="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>• Size only (sm:, md:, lg:)</li>
              <li>• Orientation only (portrait:)</li>
              <li>• Bit-depth only (1bit:, 2bit:, 4bit:)</li>
              <li>• Size + Orientation</li>
              <li>• Size + Bit-depth</li>
              <li>• Orientation + Bit-depth</li>
              <li>• All three combined</li>
            </ul>
          </div>
          <div>
            <%= framework_heading(4, 'Test Coverage') %>
                          <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <div>
                  <strong>6 Components</strong> tested across<br>
                  <strong>7 Responsive Variants</strong> each
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-500">
                  Total: 40 test combinations
                </div>
              </div>
          </div>
        </div>
      </section>


      <section class="space-y-8">
        <%= framework_heading(3, 'Responsive Utilities Test Results') %>
        
        <p class="text-gray-600 dark:text-gray-400">
          The following tables test various responsive utility components, comparing SCSS mixin implementations with CSS class implementations.
          Each component supports different combinations of size breakpoints, orientation, and bit-depth conditions.
        </p>

        <div class="space-y-12">
          <div class="space-y-6">
            <%= framework_heading(4, 'Background Component') %>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-75 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                        Test Case
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        SCSS Mixin Result
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        CSS Class Result
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-gray-75 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- Size variant -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg on md+ screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-size-md-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Orientation variant -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg in portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-orientation-portrait-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="portrait:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Bit-depth variant -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">2bit:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg on 2-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-bit-depth-2bit-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="2bit:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Size + Orientation combination -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:portrait:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', 'portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg on md+ portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-size-orientation-md-portrait-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:portrait:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Size + Bit-depth combination -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:2bit:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg on md+ 2-bit</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-size-bit-depth-md-2bit-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:2bit:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Orientation + Bit-depth combination -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:2bit:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg on portrait 2-bit</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-orientation-bit-depth-portrait-2bit-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="portrait:2bit:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- All three combined -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:portrait:2bit:bg--gray-50</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', 'portrait', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Gray bg on md+ portrait 2-bit</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="bg-all-md-portrait-2bit-mixin" class="w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:portrait:2bit:bg--gray-50 w--12 h--12 rounded--medium"></div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="space-y-6">
            <%= framework_heading(4, 'Visibility Component') %>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-75 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                        Test Case
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        SCSS Mixin Result
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        CSS Class Result
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-gray-75 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">sm:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('sm')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on sm+ screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-size-sm-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="sm:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on portrait screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-orientation-portrait-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="portrait:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">4bit:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('4bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on 4-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-bit-depth-4bit-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="4bit:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:portrait:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', 'portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on md+ portrait screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-size-orientation-md-portrait-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:portrait:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:2bit:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on lg+ 2-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-size-bit-depth-lg-2bit-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="lg:2bit:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:4bit:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait', '4bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on portrait 4-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-orientation-bit-depth-portrait-4bit-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="portrait:4bit:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:portrait:2bit:hidden</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', 'portrait', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hidden on md+ portrait 2-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="vis-all-md-portrait-2bit-hidden-mixin" class="bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:portrait:2bit:hidden bg--gray-65 w--12 h--12 rounded--medium">
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="space-y-6">
            <%= framework_heading(4, 'Text Component') %>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-75 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                        Test Case
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        SCSS Mixin Result
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        CSS Class Result
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-gray-75 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:text--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered text on lg+ screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-size-lg-center-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="lg:text--center w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:text--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered text in portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-orientation-portrait-center-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="portrait:text--center w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">2bit:text--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered text on 2-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-bit-depth-2bit-center-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="2bit:text--center w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:portrait:text--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', 'portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered on md+ portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-size-orientation-md-portrait-center-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:portrait:text--center w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:4bit:text--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg', '4bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered on lg+ 4-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-size-bit-depth-lg-4bit-center-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="lg:4bit:text--center w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:2bit:text--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered on portrait 2-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-orientation-bit-depth-portrait-2bit-center-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="portrait:2bit:text--center w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:portrait:2bit:text--right</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', 'portrait', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Right-aligned on md+ portrait 2-bit</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="text-all-md-portrait-2bit-right-mixin" class="w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="md:portrait:2bit:text--right w--full">
                              <span class="value">Aa</span>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="space-y-6">
            <%= framework_heading(4, 'Value Component') %>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-75 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                        Test Case
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        SCSS Mixin Result
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        CSS Class Result
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-gray-75 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:value--large</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Large value on md+ screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-size-md-large-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value md:value--large">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:value--large</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Large value in portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-orientation-portrait-large-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value portrait:value--large">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">4bit:value--large</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('4bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Large value on 4-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-bit-depth-4bit-large-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value 4bit:value--large">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:portrait:value--large</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg', 'portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Large value on lg+ portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-size-orientation-lg-portrait-large-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value lg:portrait:value--large">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:2bit:value--large</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md', '2bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Large value on md+ 2-bit screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-size-bit-depth-md-2bit-large-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value md:2bit:value--large">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:4bit:value--large</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait', '4bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Large value on portrait 4-bit</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-orientation-bit-depth-portrait-4bit-large-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value portrait:4bit:value--large">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:portrait:4bit:value--xlarge</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg', 'portrait', '4bit')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">XLarge on lg+ portrait 4-bit</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span id="value-all-lg-portrait-4bit-xlarge-mixin" class="value">Aa</span>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <span class="value lg:portrait:4bit:value--xlarge">Aa</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="space-y-6">
            <%= framework_heading(4, 'Flex Component') %>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-75 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                        Test Case
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        SCSS Mixin Result
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        CSS Class Result
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-gray-75 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:flex--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered on md+ screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="flex-size-md-center-mixin" class="flex flex--row w--full h--full">
                              <div class="bg--gray-65 w--12 h--12 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="flex flex--row md:flex--center w--full h--full">
                              <div class="bg--gray-65 w--12 h--12 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:flex--col</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Column layout in portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="flex flex--row w--full flex--center">
                              <div id="flex-orientation-portrait-col-mixin" class="flex flex--row gap--small">
                                <div class="bg--gray-65 w--6 h--6 rounded--small"></div>
                                <div class="bg--gray-50 w--6 h--6 rounded--small"></div>
                                <div class="bg--gray-35 w--6 h--6 rounded--small"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="flex flex--row w--full flex--center">
                              <div class="flex flex--row portrait:flex--col gap--small">
                                <div class="bg--gray-65 w--6 h--6 rounded--small"></div>
                                <div class="bg--gray-50 w--6 h--6 rounded--small"></div>
                                <div class="bg--gray-35 w--6 h--6 rounded--small"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:portrait:flex--center</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg', 'portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Centered on lg+ portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="flex-size-orientation-lg-portrait-center-mixin" class="flex flex--row w--full h--full">
                              <div class="bg--gray-65 w--12 h--12 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="flex flex--row lg:portrait:flex--center w--full h--full">
                              <div class="bg--gray-65 w--12 h--12 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="space-y-6">
            <%= framework_heading(4, 'Layout Component') %>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-75 dark:bg-gray-800">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap">
                        Test Case
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        SCSS Mixin Result
                      </th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        CSS Class Result
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-gray-75 dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    <!-- Size variant - layout direction -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">md:layout--col</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('md')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Column layout on md+ screens</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="layout-size-md-col-mixin" class="layout layout--row p--small h--full w--full">
                              <div class="bg--gray-65 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-50 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-35 w--16 h--16 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="layout layout--row md:layout--col p--small h--full w--full">
                              <div class="bg--gray-65 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-50 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-35 w--16 h--16 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Orientation variant - alignment -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">portrait:layout--bottom</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Center layout in portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="layout-orientation-portrait-center-mixin" class="layout p--small h--full">
                              <div class="bg--gray-65 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-50 w--16 h--16 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="layout layout--row portrait:layout--bottom p--small h--full">
                              <div class="bg--gray-65 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-50 w--16 h--16 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    
                    <!-- Size + Orientation combination - complex layout change -->
                    <tr>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-mono text-gray-900 dark:text-gray-100">lg:portrait:layout--bottom</div>
                        <div class="text-xs font-mono text-gray-600 dark:text-gray-300 mt-1">@include screen.screen('lg', 'portrait')</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Bottom alignment on lg+ portrait</div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div id="layout-size-orientation-lg-portrait-bottom-mixin" class="layout p--small h--full">
                              <div class="bg--gray-65 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-50 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-35 w--16 h--16 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="px-6 py-4">
                        <div class="screen-container trmnl <%= framework_example_wrapper_classes %>">
                          <div class="screen">
                            <div class="layout lg:portrait:layout--bottom p--small h--full">
                              <div class="bg--gray-65 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-50 w--16 h--16 rounded--small"></div>
                              <div class="bg--gray-35 w--16 h--16 rounded--small"></div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Component Coverage Section -->
      <section class="space-y-8">
        <%= framework_heading(3, 'Component Coverage') %>
        
        <div class="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-3">Components Tested</h4>
              <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Background (all 7 variants)</li>
                <li>• Visibility (all 7 variants)</li>
                <li>• Text (all 7 variants)</li>
                <li>• Value (all 7 variants)</li>
                <li>• Flex (size + orientation only)</li>
                <li>• Layout (size + orientation only)</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-3">Variants Tested</h4>
              <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Size only (sm:, md:, lg:)</li>
                <li>• Orientation only (portrait:)</li>
                <li>• Bit-depth only (1bit:, 2bit:, 4bit:)</li>
                <li>• Size + Orientation</li>
                <li>• Size + Bit-depth</li>
                <li>• Orientation + Bit-depth</li>
                <li>• All three combined</li>
              </ul>
            </div>
            <div>
              <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-3">Components Not Yet Tested</h4>
              <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Spacing (p--, m--, etc.)</li>
                <li>• Gap (gap--)</li>
                <li>• Size (w--, h--)</li>
                <li>• Rounded (rounded--)</li>
                <li>• Border (border--)</li>
                <li>• Grid (grid--, col--)</li>
                <li>• Clamp (clamp--)</li>
                <li>• Effects (crisp-edges)</li>
                <li>• Image (image--)</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <%= render 'framework/page_navigation' %>
    </div>
  </div>
</div>

<script>
// Test configuration state
let testConfig = {
  size: 'md',
  orientation: 'landscape',
  bitDepth: '4bit'
};

// Map sizes to actual viewport dimensions
const sizeMap = {
  sm: { width: 480, height: 640 },
  md: { width: 768, height: 1024 },
  lg: { width: 1024, height: 768 }
};

// Global variable to track example size state
let isExampleSizeSmall = true; // Default to "Sample" (small)

function toggleScreenSizes(isSmall) {
  document.querySelectorAll('.screen').forEach(container => {
    if (isSmall) {
      container.style.transformOrigin = 'top left';
      container.style.width = '120px';
      container.style.height = '120px';
    } else {
      container.style.transform = '';
      container.style.transformOrigin = '';
      container.style.width = '';
      container.style.height = '';
    }
  });
}

// Update example size button states in the UI
function updateExampleSizeUI() {
  document.querySelectorAll('[data-example-size]').forEach(btn => {
    const isSample = btn.dataset.exampleSize === 'sample';
    const isActive = (isSample && isExampleSizeSmall) || (!isSample && !isExampleSizeSmall);
    
    if (isActive) {
      // Add active state classes
      btn.classList.remove('text-sage-500', 'bg-transparent', 'dark:text-sage-400');
      btn.classList.add('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700');
      // Update hover classes for active state
      btn.classList.remove('hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
      btn.classList.add('hover:bg-sage-200', 'dark:hover:bg-sage-600');
    } else {
      // Remove active state classes
      btn.classList.remove('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700', 'hover:bg-sage-200', 'dark:hover:bg-sage-600');
      btn.classList.add('text-sage-500', 'bg-transparent', 'dark:text-sage-400', 'hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
    }
  });
}

// Handle example size button clicks
function handleExampleSizeClick(e) {
  const btn = e.target.closest('.example-size-btn');
  if (!btn) return;
  
  const isSample = btn.dataset.exampleSize === 'sample';
  isExampleSizeSmall = isSample;
  
  updateExampleSizeUI();
  toggleScreenSizes(isExampleSizeSmall);
}

// Load initial test configuration from framework's localStorage (one-time on page load)
function loadTestConfig() {
  // Get the current device configuration from localStorage
  const storedDevice = localStorage.getItem('selectedFrameworkDevice');
  const storedOrientation = localStorage.getItem('frameworkOrientation');
  
  // Default values
  let size = 'md';
  let orientation = 'landscape';
  let bitDepth = '4bit';
  
  // Device configurations matching the framework
  const deviceConfigs = <%= framework_device_configs.to_json.html_safe %>;

  // Parse device configuration
  if (storedDevice && storedDevice !== 'null' && deviceConfigs[storedDevice]) {
    const deviceConfig = deviceConfigs[storedDevice];
    size = deviceConfig.size;
    bitDepth = deviceConfig.bitDepth;
  }
  
  // Handle orientation - framework stores 'true' for portrait, false/null for landscape
  if (storedOrientation === 'true') {
    orientation = 'portrait';
  }
  
  testConfig = {
    size: size,
    orientation: orientation,
    bitDepth: bitDepth
  };
  
  console.log('Loaded initial test config from framework:', {
    storedDevice,
    storedOrientation,
    testConfig
  });
  
  updateTestConfigUI();
  applyTestConfig();
}

// Update button states in the UI
function updateTestConfigUI() {
  // Update size buttons
  document.querySelectorAll('[data-test-size]').forEach(btn => {
    const isActive = btn.dataset.testSize === testConfig.size;
    if (isActive) {
      // Add active state classes
      btn.classList.remove('text-sage-500', 'bg-transparent', 'dark:text-sage-400');
      btn.classList.add('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700');
      // Update hover classes for active state
      btn.classList.remove('hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
      btn.classList.add('hover:bg-sage-200', 'dark:hover:bg-sage-600');
    } else {
      // Remove active state classes
      btn.classList.remove('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700', 'hover:bg-sage-200', 'dark:hover:bg-sage-600');
      btn.classList.add('text-sage-500', 'bg-transparent', 'dark:text-sage-400', 'hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
    }
  });
  
  // Update orientation buttons
  document.querySelectorAll('[data-test-orientation]').forEach(btn => {
    const isActive = btn.dataset.testOrientation === testConfig.orientation;
    if (isActive) {
      // Add active state classes
      btn.classList.remove('text-sage-500', 'bg-transparent', 'dark:text-sage-400');
      btn.classList.add('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700');
      // Update hover classes for active state
      btn.classList.remove('hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
      btn.classList.add('hover:bg-sage-200', 'dark:hover:bg-sage-600');
    } else {
      // Remove active state classes
      btn.classList.remove('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700', 'hover:bg-sage-200', 'dark:hover:bg-sage-600');
      btn.classList.add('text-sage-500', 'bg-transparent', 'dark:text-sage-400', 'hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
    }
  });
  
  // Update bit depth buttons
  document.querySelectorAll('[data-test-bitdepth]').forEach(btn => {
    const isActive = btn.dataset.testBitdepth === testConfig.bitDepth;
    if (isActive) {
      // Add active state classes
      btn.classList.remove('text-sage-500', 'bg-transparent', 'dark:text-sage-400');
      btn.classList.add('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700');
      // Update hover classes for active state
      btn.classList.remove('hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
      btn.classList.add('hover:bg-sage-200', 'dark:hover:bg-sage-600');
    } else {
      // Remove active state classes
      btn.classList.remove('text-sage-600', 'bg-sage-100', 'dark:text-sage-300', 'dark:bg-sage-700', 'hover:bg-sage-200', 'dark:hover:bg-sage-600');
      btn.classList.add('text-sage-500', 'bg-transparent', 'dark:text-sage-400', 'hover:text-sage-700', 'hover:bg-sage-100', 'dark:hover:text-sage-200', 'dark:hover:bg-sage-800');
    }
  });
}

// Apply configuration to all test screens
function applyTestConfig() {
  const screens = document.querySelectorAll('.screen');
  
  screens.forEach(screen => {
    // Remove all screen modifier classes
    screen.className = screen.className.replace(/\bscreen--(sm|md|lg|portrait|landscape|1bit|2bit|4bit)\b/g, '').trim();
    
    // Add base screen class if not present
    if (!screen.classList.contains('screen')) {
      screen.classList.add('screen');
    }
    
    // Add new modifier classes based on configuration
    screen.classList.add(`screen--${testConfig.size}`);
    screen.classList.add(`screen--${testConfig.orientation}`);
    screen.classList.add(`screen--${testConfig.bitDepth}`);
  });
}

// Handle button clicks
function handleTestConfigClick(e) {
  const btn = e.target.closest('.test-config-btn');
  if (!btn) return;
  
  if (btn.dataset.testSize) {
    testConfig.size = btn.dataset.testSize;
  } else if (btn.dataset.testOrientation) {
    testConfig.orientation = btn.dataset.testOrientation;
  } else if (btn.dataset.testBitdepth) {
    testConfig.bitDepth = btn.dataset.testBitdepth;
  }
  
  updateTestConfigUI();
  applyTestConfig();
}





// Global variable to store the polling interval (legacy - kept for cleanup)
let responsiveTestPollingInterval = null;

// Function to initialize all responsive test functionality
function initializeResponsiveTest() {
  // Clear any existing interval
  if (responsiveTestPollingInterval) {
    clearInterval(responsiveTestPollingInterval);
  }
  
  // Set up test configuration buttons
  document.querySelectorAll('.test-config-btn').forEach(btn => {
    btn.addEventListener('click', handleTestConfigClick);
  });
  
  // Set up example size buttons
  document.querySelectorAll('.example-size-btn').forEach(btn => {
    btn.addEventListener('click', handleExampleSizeClick);
  });
  
  // Load initial configuration from framework and initialize
  loadTestConfig();
  updateExampleSizeUI();
  toggleScreenSizes(isExampleSizeSmall);
}

// Clean up function
function cleanupResponsiveTest() {
  if (responsiveTestPollingInterval) {
    clearInterval(responsiveTestPollingInterval);
    responsiveTestPollingInterval = null;
  }
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
  initializeResponsiveTest();
});

// Re-initialize on Turbo load
document.addEventListener('turbo:load', function() {
  initializeResponsiveTest();
});

// Clean up before navigating away
document.addEventListener('turbo:before-visit', function() {
  cleanupResponsiveTest();
});
</script>

