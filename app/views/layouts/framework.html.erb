<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
<head>
  <%= metamagic site: Rails.application.credentials.company_name, title: [:site, :title], separator: " | " %>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;350;375;400;450;600;700&family=EB+Garamond:ital,wght@0,400..800;1,400..800&display=swap" rel="stylesheet">

  <%= javascript_importmap_tags %>

  <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
  <%= stylesheet_link_tag "application.tailwind", "data-turbo-track": "reload" %>
  
  <!-- Hide screens until classes are applied to prevent FOUC -->
  <style>
    .screen:not([class*="screen--"]) {
      visibility: hidden;
    }
  </style>

  <% if params[:controller] == 'framework_legacy' %>
    <%= stylesheet_link_tag "plugins_legacy", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "plugin-render/plugins_legacy", "data-turbo-track": "reload" %>
  <% else %>
    <%= stylesheet_link_tag "plugins", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "plugin-render/plugins", "data-turbo-track": "reload" %>
  <% end %>

  <%= render "shared/meta" %>
  <%= render 'shared/favicon' %>

  <script type="text/javascript" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <%= render "shared/script_tags" %>
  
  <!-- Prevent FOUC by applying saved settings immediately -->
  <script>
    (function() {
      // Get saved settings
      const savedDevice = localStorage.getItem('selectedFrameworkDevice') || 'og_png';
      const isDarkMode = localStorage.getItem('frameworkDarkMode') === 'true';
      const isPortrait = localStorage.getItem('frameworkOrientation') === 'true';
      const deviceConfig = window.DEVICE_CONFIGS[savedDevice];
      
      // Get saved bit depth or use device's default
      let savedBitDepth = localStorage.getItem('selectedFrameworkBitDepth');
      if (!savedBitDepth) {
        savedBitDepth = deviceConfig.bitDepth;
      } else {
        savedBitDepth = parseInt(savedBitDepth);
        // Ensure bit depth doesn't exceed device max
        if (savedBitDepth > deviceConfig.bitDepth) {
          savedBitDepth = deviceConfig.bitDepth;
        }
      }
      
      // Apply classes immediately when elements are parsed
      // This runs synchronously as the DOM is being built
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1 && node.classList && node.classList.contains('screen')) {
              // Apply all classes immediately
              node.classList.add(`screen--${savedDevice}`);
              if (deviceConfig) {
                node.classList.add(`screen--${savedBitDepth}bit`);
                node.classList.add(`screen--${deviceConfig.size}`);
              }
              if (isDarkMode) {
                node.classList.add('screen--dark-mode');
              }
              // Apply orientation classes (default is landscape)
              if (isPortrait) {
                node.classList.add('screen--portrait');
              } else {
                node.classList.add('screen--landscape');
              }
            }
            // Also check children
            if (node.querySelectorAll) {
              node.querySelectorAll('.screen').forEach(screen => {
                screen.classList.add(`screen--${savedDevice}`);
                if (deviceConfig) {
                  screen.classList.add(`screen--${savedBitDepth}bit`);
                  screen.classList.add(`screen--${deviceConfig.size}`);
                }
                if (isDarkMode) {
                  screen.classList.add('screen--dark-mode');
                }
                // Apply orientation classes (default is landscape)
                if (isPortrait) {
                  screen.classList.add('screen--portrait');
                } else {
                  screen.classList.add('screen--landscape');
                }
              });
            }
          });
        });
      });
      
      // Start observing immediately
      observer.observe(document.documentElement, {
        childList: true,
        subtree: true
      });
      
      // Stop observing after DOMContentLoaded
      document.addEventListener('DOMContentLoaded', function() {
        observer.disconnect();
        
        // Final pass to catch any missed elements
        document.querySelectorAll('.screen').forEach(screen => {
          if (!screen.classList.contains(`screen--${savedDevice}`)) {
            screen.classList.add(`screen--${savedDevice}`);
            if (deviceConfig) {
              screen.classList.add(`screen--${savedBitDepth}bit`);
              screen.classList.add(`screen--${deviceConfig.size}`);
            }
            if (isDarkMode) {
              screen.classList.add('screen--dark-mode');
            }
            // Apply orientation classes (default is landscape)
            if (isPortrait) {
              screen.classList.add('screen--portrait');
            } else {
              screen.classList.add('screen--landscape');
            }
          }
        });
      });
    })();
  </script>
</head>
<body class="<%= layout_application %>" data-controller="audio multi-clipboard">
  <%= render "framework/nav" %>

  <!-- Main layout container -->
  <div class="flex w-full pt-[68px] min-h-screen max-w-9xl">
    <% unless params[:action] == 'index' %>
    <!-- Left Sidebar -->
    <div id="left-sidebar-wrapper" class="sidebar-wrapper">
      <div id="left-sidebar" class="md:opacity-100 opacity-0 md:translate-x-0 -translate-x-full pointer-events-none md:pointer-events-auto md:sticky md:top-16 fixed top-16 w-0 shrink-0 bg-gray-100 dark:bg-gray-850 border-r border-gray-200 dark:border-gray-800 h-[calc(100vh-4rem)] overflow-hidden">
        <%= render "framework/sidebar", sidebar_type: "left" %>
      </div>
    </div>
    <% end %>

    <!-- Main Content Column -->
    <div id="main-content-column" class="flex-grow min-w-0">
            <% if params[:action] == 'responsive_test' %>
      <!-- Test Configuration Navigation - only shown on responsive_test page -->
      <!-- Sticky sentinel positioned exactly where sticky trigger happens -->
      <div id="sticky-sentinel" class="h-px mt-8 -mb-px"></div>
      <div class="sticky top-[68px] z-30">
        <div class="w-full max-w-5xl mx-auto px-2 md:px-4">
          <div class="flex flex-col">
            <div id="test-config-container" class="p-4 rounded rounded-lg flex flex-col lg:flex-row gap-4 bg-sage-200/60 dark:bg-sage-850/90 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50">
              <!-- Screen Size Section -->
              <div class="flex-1">
                <div class="text-xs font-medium text-sage-500 dark:text-sage-400 uppercase tracking-wider mb-2 px-2">Screen Size</div>
                <div class="flex gap-1">
                  <button data-test-size="sm" class="test-config-btn <%= button_sage_classes %>">
                    Small
                  </button>
                  <button data-test-size="md" class="test-config-btn <%= button_sage_classes %>">
                    Medium
                  </button>
                  <button data-test-size="lg" class="test-config-btn <%= button_sage_classes %>">
                    Large
                  </button>
                </div>
              </div>

              <div class="hidden lg:block w-px bg-sage-250 dark:bg-sage-700 self-stretch my-2"></div>

              <!-- Orientation Section -->
              <div class="flex-1">
                <div class="text-xs font-medium text-sage-500 dark:text-sage-400 uppercase tracking-wider mb-2 px-2">Orientation</div>
                <div class="flex gap-1">
                  <button data-test-orientation="landscape" class="test-config-btn <%= button_sage_classes %>">
                    Landscape
                  </button>
                  <button data-test-orientation="portrait" class="test-config-btn <%= button_sage_classes %>">
                    Portrait
                  </button>
                </div>
              </div>

              <div class="hidden lg:block w-px bg-sage-250 dark:bg-sage-700 self-stretch my-2"></div>

              <!-- Bit Depth Section -->
              <div class="flex-1">
                <div class="text-xs font-medium text-sage-500 dark:text-sage-400 uppercase tracking-wider mb-2 px-2">Bit Depth</div>
                <div class="flex gap-1">
                  <% framework_bit_depth_options.each do |label, value| %>
                    <button data-test-bitdepth="<%= value %>" class="test-config-btn <%= button_sage_classes %>">
                      <%= label %>
                    </button>
                  <% end %>
                </div>
              </div>

              <div class="hidden lg:block w-px bg-sage-250 dark:bg-sage-700 self-stretch my-2"></div>

              <!-- Examples Section -->
              <div class="flex-1">
                <div class="text-xs font-medium text-sage-500 dark:text-sage-400 uppercase tracking-wider mb-2 px-2">Examples</div>
                <div class="flex gap-1">
                  <button data-example-size="sample" class="example-size-btn <%= button_sage_active_classes %>">
                    Sample
                  </button>
                  <button data-example-size="original" class="example-size-btn <%= button_sage_classes %>">
                    Original
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <% end %>
      
      <main id="main-content" class="overflow-auto flex justify-center">
        <%= yield %>
      </main>
    </div>

    <% unless params[:action] == 'index' %>
    <!-- Right Sidebar -->
    <div id="right-sidebar-wrapper" class="sidebar-wrapper">
      <div id="right-sidebar" class="md:opacity-100 opacity-0 md:translate-x-0 translate-x-full pointer-events-none md:pointer-events-auto md:sticky md:top-16 fixed top-16 w-0 shrink-0 bg-gray-100 dark:bg-gray-850 border-l border-gray-200 dark:border-gray-800 h-[calc(100vh-4rem)] overflow-hidden">
        <%= render "framework/sidebar", sidebar_type: "right" %>
      </div>
    </div>
    <% end %>
  </div>
  
  <% unless params[:action] == 'index' %>
  <!-- Bottom navigation control -->
  <%= render "framework/sidebar_nav" %>
  
  <!-- Mobile sidebars (outside the main layout for proper z-index) -->
  <!-- Mobile Left Sidebar -->
  <div id="left-sidebar-mobile" class="mobile-sidebar fixed inset-0 bg-black bg-opacity-50 hidden z-50 transition-opacity duration-300 ease-in-out opacity-0">
    <div class="absolute inset-y-0 left-0 w-64 bg-white dark:bg-gray-900 shadow-xl transition transform duration-300 ease-in-out -translate-x-full">
      <div class="fixed top-0 left-0 right-0 z-50 pointer-events-none flex justify-end items-center p-1">
        <button data-sidebar-close="left" class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-550 dark:hover:text-gray-400 pointer-events-auto">
          <%= render "shared/icons/close" %>
        </button>
      </div>
      <div class="overflow-y-auto h-full pb-20 pt-16">
        <%= render "framework/sidebar", sidebar_type: "left" %>
      </div>
    </div>
  </div>
  
  <!-- Mobile Right Sidebar -->
  <div id="right-sidebar-mobile" class="mobile-sidebar fixed inset-0 bg-black bg-opacity-50 hidden z-50 transition-opacity duration-300 ease-in-out opacity-0">
    <div class="absolute inset-y-0 right-0 w-64 bg-white dark:bg-gray-900 shadow-xl transition transform duration-300 ease-in-out translate-x-full">
      <div class="fixed top-0 left-0 right-0 z-50 pointer-events-none flex justify-end items-center p-1">
        <button data-sidebar-close="right" class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-550 dark:hover:text-gray-400 pointer-events-auto">
          <%= render "shared/icons/close" %>
        </button>
      </div>
      <div class="overflow-y-auto h-full pb-20 pt-4">
        <%= render "framework/sidebar", sidebar_type: "right" %>
      </div>
    </div>
  </div>
  <% end %>
  
  <script type="text/javascript">
    
    document.addEventListener('DOMContentLoaded', function() {
      window.DEVICE_CONFIGS = <%= framework_device_configs.to_json.html_safe %>;
      window.DEVICE_CLASSES = <%= framework_device_configs.map { |key, _| "screen--#{key}" }.uniq.to_json.html_safe %>;
      window.BIT_DEPTH_CLASSES = <%= framework_device_configs.map { |_, config| "screen--#{config['bitDepth']}bit" }.uniq.to_json.html_safe %>;
      window.SCREEN_SIZES = <%= framework_device_configs.map { |_, config| "screen--#{config['size']}" }.uniq.to_json.html_safe %>;
      window.BIT_DEPTH_OPTIONS = <%= framework_device_configs.map { |_, config| config['bitDepth'] }.uniq.sort.to_json.html_safe %>;

      // Initialize TOC generation
      generateTableOfContents();
      
      <% unless params[:action] == 'index' %>
      // Initialize sidebar controls
      initSidebars();
      <% end %>
      
      // Initialize device selector
      initDeviceSelector();
      
      // Initialize dark mode toggle
      initDarkModeToggle();
      
      // Initialize orientation toggle
      initOrientationToggle();
      
      <% if params[:action] == 'responsive_test' %>
      // Initialize sticky border radius handler
      initStickyBorderRadius();
      <% end %>
      
      // Ensure all Framework docs load with terminalize() function
      Array.from(document.querySelectorAll('[href*="/framework/"]')).forEach(docs => { 
        docs.dataset.turbo = false; 
      });
    });
    
    // Initialize device selector functionality
    function initDeviceSelector() {
      const DEVICE_STORAGE_KEY = 'selectedFrameworkDevice';
      const BIT_DEPTH_STORAGE_KEY = 'selectedFrameworkBitDepth';
      const DEFAULT_DEVICE = 'og_png';
      
      // Get saved device or use default
      let currentDevice = localStorage.getItem(DEVICE_STORAGE_KEY) || DEFAULT_DEVICE;
      
      // Get saved bit depth or use device's default
      let currentBitDepth = localStorage.getItem(BIT_DEPTH_STORAGE_KEY);
      if (!currentBitDepth) {
        currentBitDepth = window.DEVICE_CONFIGS[currentDevice].bitDepth;
      } else {
        currentBitDepth = parseInt(currentBitDepth);
      }

      // Apply device and bit-depth classes to all screen elements
      function applyDeviceAndBitDepth(device, bitDepth) {
        const deviceConfig = window.DEVICE_CONFIGS[device];

        // Get current orientation state from localStorage
        const isPortrait = localStorage.getItem('frameworkOrientation') === 'true';
        
        document.querySelectorAll('.screen').forEach(screen => {
          // Remove all device-related classes
          const orientationClasses = ['screen--portrait', 'screen--landscape'];
          
          // Remove all existing classes
          window.DEVICE_CLASSES.forEach(deviceClass => {
            screen.classList.remove(deviceClass);
          });
          
          window.BIT_DEPTH_CLASSES.forEach(bitDepthClass => {
            screen.classList.remove(bitDepthClass);
          });
          
          window.SCREEN_SIZES.forEach(sizeClass => {
            screen.classList.remove(sizeClass);
          });
          
          orientationClasses.forEach(orientationClass => {
            screen.classList.remove(orientationClass);
          });
          
          // Add the selected device class
          screen.classList.add(`screen--${device}`);
          
          // Add the selected bit-depth class (use parameter, not device default)
          screen.classList.add(`screen--${bitDepth}bit`);
          
          // Add the size class based on device configuration
          if (deviceConfig && deviceConfig.size) {
            screen.classList.add(`screen--${deviceConfig.size}`);
          }
          
          // Apply orientation classes
          // Default is landscape (no class needed), portrait adds screen--portrait
          if (isPortrait) {
            screen.classList.add('screen--portrait');
          } else {
            // Explicitly add landscape class for clarity (optional)
            screen.classList.add('screen--landscape');
          }
        });
      }
      
      // Update the device selector dropdown to show current device
      function updateDeviceSelector(device) {
        const selector = document.getElementById('device-selector');
        const selectorMobile = document.getElementById('device-selector-mobile');
        if (selector) {
          selector.value = device;
        }
        if (selectorMobile) {
          selectorMobile.value = device;
        }
      }
      
      // Update the bit-depth selector dropdown to show current bit depth
      function updateBitDepthSelector(bitDepth) {
        const selector = document.getElementById('bit-depth-selector');
        const selectorMobile = document.getElementById('bit-depth-selector-mobile');
        if (selector) {
          selector.value = bitDepth;
        }
        if (selectorMobile) {
          selectorMobile.value = bitDepth;
        }
      }
      
      // Check if current configuration matches device defaults
      function isAtDeviceDefaults(device, bitDepth) {
        const defaultBitDepth = window.DEVICE_CONFIGS[device].bitDepth;
        return parseInt(bitDepth) === defaultBitDepth;
      }
      
      // Update reset button state based on current configuration
      function updateResetButtonState(device, bitDepth) {
        const resetButton = document.getElementById('reset-device-defaults');
        const resetButtonMobile = document.getElementById('reset-device-defaults-mobile');
        const isAtDefaults = isAtDeviceDefaults(device, bitDepth);
        
        [resetButton, resetButtonMobile].forEach(button => {
          if (button) {
            if (isAtDefaults) {
              button.disabled = true;
              button.style.opacity = '0.5';
              button.style.cursor = 'default';
              button.setAttribute('aria-disabled', 'true');
            } else {
              button.disabled = false;
              button.style.opacity = '1';
              button.style.cursor = 'pointer';
              button.removeAttribute('aria-disabled');
            }
          }
        });
      }
      
      // Update available bit-depth options based on device max
      function updateBitDepthOptions(device) {
        const maxBitDepth = window.DEVICE_CONFIGS[device].bitDepth;
        const bitDepthSelector = document.getElementById('bit-depth-selector');
        const bitDepthSelectorMobile = document.getElementById('bit-depth-selector-mobile');
        
        // All possible bit depth options
        const allOptions = window.BIT_DEPTH_OPTIONS.map(bitDepth => [`${bitDepth}-bit`, bitDepth]);
        
        // Update desktop selector
        if (bitDepthSelector) {
          bitDepthSelector.innerHTML = '';
          allOptions.forEach(([text, value]) => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            
            // Disable if bit depth exceeds device maximum
            if (parseInt(value) > maxBitDepth) {
              option.disabled = true;
            }
            
            bitDepthSelector.appendChild(option);
          });
          
          // Reset to max if current selection is too high, or set to max if no current value
          const currentValue = parseInt(bitDepthSelector.value);
          if (!currentValue || currentValue > maxBitDepth) {
            bitDepthSelector.value = maxBitDepth;
          }
        }
        
        // Update mobile selector
        if (bitDepthSelectorMobile) {
          bitDepthSelectorMobile.innerHTML = '';
          allOptions.forEach(([text, value]) => {
            const option = document.createElement('option');
            option.value = value;
            option.textContent = text;
            
            // Disable if bit depth exceeds device maximum
            if (parseInt(value) > maxBitDepth) {
              option.disabled = true;
            }
            
            bitDepthSelectorMobile.appendChild(option);
          });
          
          // Reset to max if current selection is too high, or set to max if no current value
          const currentValue = parseInt(bitDepthSelectorMobile.value);
          if (!currentValue || currentValue > maxBitDepth) {
            bitDepthSelectorMobile.value = maxBitDepth;
          }
        }
      }
      
      // Handle device selection change
      const deviceSelector = document.getElementById('device-selector');
      const deviceSelectorMobile = document.getElementById('device-selector-mobile');
      
      if (deviceSelector) {
        deviceSelector.addEventListener('change', function(e) {
          const selectedDevice = e.target.value;
          
          // Capture current example position before making changes
          const currentExamplePosition = getCurrentExamplePosition();
          
          // Save to localStorage
          localStorage.setItem(DEVICE_STORAGE_KEY, selectedDevice);
          currentDevice = selectedDevice;
          
          // Update available bit-depth options
          updateBitDepthOptions(selectedDevice);
          
          // Set to maximum available bit depth for new device
          const maxBitDepth = window.DEVICE_CONFIGS[selectedDevice].bitDepth;
          currentBitDepth = maxBitDepth;
          localStorage.setItem(BIT_DEPTH_STORAGE_KEY, currentBitDepth);
          updateBitDepthSelector(currentBitDepth);
          
          // Apply device and bit-depth classes to all screens
          applyDeviceAndBitDepth(selectedDevice, currentBitDepth);
          
          // Update mobile selector to match
          if (deviceSelectorMobile) {
            deviceSelectorMobile.value = selectedDevice;
          }
          
          // Update reset button state
          updateResetButtonState(currentDevice, currentBitDepth);
          
          // Run terminalize function after device change
          terminalize();
          
          // Restore scroll position after layout settles
          restoreScrollPosition(currentExamplePosition);
        });
      }
      
      if (deviceSelectorMobile) {
        deviceSelectorMobile.addEventListener('change', function(e) {
          const selectedDevice = e.target.value;
          
          // Capture current example position before making changes
          const currentExamplePosition = getCurrentExamplePosition();
          
          // Save to localStorage
          localStorage.setItem(DEVICE_STORAGE_KEY, selectedDevice);
          currentDevice = selectedDevice;
          
          // Update available bit-depth options
          updateBitDepthOptions(selectedDevice);
          
          // Set to maximum available bit depth for new device
          const maxBitDepth = window.DEVICE_CONFIGS[selectedDevice].bitDepth;
          currentBitDepth = maxBitDepth;
          localStorage.setItem(BIT_DEPTH_STORAGE_KEY, currentBitDepth);
          updateBitDepthSelector(currentBitDepth);
          
          // Apply device and bit-depth classes to all screens
          applyDeviceAndBitDepth(selectedDevice, currentBitDepth);
          
          // Update desktop selector to match
          if (deviceSelector) {
            deviceSelector.value = selectedDevice;
          }
          
          // Update reset button state
          updateResetButtonState(currentDevice, currentBitDepth);
          
          // Run terminalize function after device change
          terminalize();
          
          // Restore scroll position after layout settles
          restoreScrollPosition(currentExamplePosition);
        });
      }
      
      // Handle bit-depth selection change
      const bitDepthSelector = document.getElementById('bit-depth-selector');
      const bitDepthSelectorMobile = document.getElementById('bit-depth-selector-mobile');
      
      if (bitDepthSelector) {
        bitDepthSelector.addEventListener('change', function(e) {
          const selectedBitDepth = parseInt(e.target.value);
          
          // Capture current example position before making changes
          const currentExamplePosition = getCurrentExamplePosition();
          
          // Save to localStorage
          localStorage.setItem(BIT_DEPTH_STORAGE_KEY, selectedBitDepth);
          currentBitDepth = selectedBitDepth;
          
          // Apply device and bit-depth classes to all screens
          applyDeviceAndBitDepth(currentDevice, selectedBitDepth);
          
          // Update mobile selector to match
          if (bitDepthSelectorMobile) {
            bitDepthSelectorMobile.value = selectedBitDepth;
          }
          
          // Update reset button state
          updateResetButtonState(currentDevice, currentBitDepth);
          
          // Run terminalize function after bit-depth change
          terminalize();
          
          // Restore scroll position after layout settles
          restoreScrollPosition(currentExamplePosition);
        });
      }
      
      if (bitDepthSelectorMobile) {
        bitDepthSelectorMobile.addEventListener('change', function(e) {
          const selectedBitDepth = parseInt(e.target.value);
          
          // Capture current example position before making changes
          const currentExamplePosition = getCurrentExamplePosition();
          
          // Save to localStorage
          localStorage.setItem(BIT_DEPTH_STORAGE_KEY, selectedBitDepth);
          currentBitDepth = selectedBitDepth;
          
          // Apply device and bit-depth classes to all screens
          applyDeviceAndBitDepth(currentDevice, selectedBitDepth);
          
          // Update desktop selector to match
          if (bitDepthSelector) {
            bitDepthSelector.value = selectedBitDepth;
          }
          
          // Update reset button state
          updateResetButtonState(currentDevice, currentBitDepth);
          
          // Run terminalize function after bit-depth change
          terminalize();
          
          // Restore scroll position after layout settles
          restoreScrollPosition(currentExamplePosition);
        });
      }
      
      // Handle reset to defaults button
      const resetButton = document.getElementById('reset-device-defaults');
      const resetButtonMobile = document.getElementById('reset-device-defaults-mobile');
      
      if (resetButton) {
        resetButton.addEventListener('click', function() {
          // Don't proceed if button is disabled
          if (resetButton.disabled) return;
          
          // Reset to device's default bit depth
          const defaultBitDepth = window.DEVICE_CONFIGS[currentDevice].bitDepth;
          
          // Capture current example position before making changes
          const currentExamplePosition = getCurrentExamplePosition();
          
          // Save to localStorage
          localStorage.setItem(BIT_DEPTH_STORAGE_KEY, defaultBitDepth);
          currentBitDepth = defaultBitDepth;
          
          // Update selectors
          updateBitDepthSelector(defaultBitDepth);
          
          // Apply device and bit-depth classes to all screens
          applyDeviceAndBitDepth(currentDevice, defaultBitDepth);
          
          // Update reset button state (will be disabled since we're now at defaults)
          updateResetButtonState(currentDevice, currentBitDepth);
          
          // Run terminalize function
          terminalize();
          
          // Restore scroll position after layout settles
          restoreScrollPosition(currentExamplePosition);
        });
      }
      
      if (resetButtonMobile) {
        resetButtonMobile.addEventListener('click', function() {
          // Don't proceed if button is disabled
          if (resetButtonMobile.disabled) return;
          
          // Reset to device's default bit depth
          const defaultBitDepth = window.DEVICE_CONFIGS[currentDevice].bitDepth;
          
          // Capture current example position before making changes
          const currentExamplePosition = getCurrentExamplePosition();
          
          // Save to localStorage
          localStorage.setItem(BIT_DEPTH_STORAGE_KEY, defaultBitDepth);
          currentBitDepth = defaultBitDepth;
          
          // Update selectors
          updateBitDepthSelector(defaultBitDepth);
          
          // Apply device and bit-depth classes to all screens
          applyDeviceAndBitDepth(currentDevice, defaultBitDepth);
          
          // Update reset button state (will be disabled since we're now at defaults)
          updateResetButtonState(currentDevice, currentBitDepth);
          
          // Run terminalize function
          terminalize();
          
          // Restore scroll position after layout settles
          restoreScrollPosition(currentExamplePosition);
        });
      }
      
      // Apply saved device and bit depth on page load
      updateDeviceSelector(currentDevice);
      updateBitDepthOptions(currentDevice);
      updateBitDepthSelector(currentBitDepth);
      applyDeviceAndBitDepth(currentDevice, currentBitDepth);
      
      // Update reset button state on initial load
      updateResetButtonState(currentDevice, currentBitDepth);
      
      // Re-apply device and bit depth when new content is loaded (for Turbo)
      document.addEventListener('turbo:load', function() {
        const savedDevice = localStorage.getItem(DEVICE_STORAGE_KEY) || DEFAULT_DEVICE;
        if (!window.DEVICE_CONFIGS[savedDevice]) {
          console.warn(`Device "${savedDevice}" not found in DEVICE_CONFIGS, falling back to default.`);
          savedDevice = DEFAULT_DEVICE;
        }
        const savedBitDepth = parseInt(localStorage.getItem(BIT_DEPTH_STORAGE_KEY) || window.DEVICE_CONFIGS[savedDevice].bitDepth);
        updateDeviceSelector(savedDevice);
        updateBitDepthOptions(savedDevice);
        updateBitDepthSelector(savedBitDepth);
        applyDeviceAndBitDepth(savedDevice, savedBitDepth);
        updateResetButtonState(savedDevice, savedBitDepth);
      });
      
      // Re-apply device and bit depth after Turbo frame updates
      document.addEventListener('turbo:frame-load', function() {
        const savedDevice = localStorage.getItem(DEVICE_STORAGE_KEY) || DEFAULT_DEVICE;
        const savedBitDepth = parseInt(localStorage.getItem(BIT_DEPTH_STORAGE_KEY) || window.DEVICE_CONFIGS[savedDevice].bitDepth);
        applyDeviceAndBitDepth(savedDevice, savedBitDepth);
      });
    }
    
    // Initialize dark mode toggle functionality
    function initDarkModeToggle() {
      const DARK_MODE_STORAGE_KEY = 'frameworkDarkMode';
      const darkModeToggle = document.getElementById('dark-mode-toggle');
      const darkModeToggleMobile = document.getElementById('dark-mode-toggle-mobile');
      const lightIcon = document.getElementById('light-icon');
      const darkIcon = document.getElementById('dark-icon');
      const lightIconMobile = document.getElementById('light-icon-mobile');
      const darkIconMobile = document.getElementById('dark-icon-mobile');
      const darkModeTextMobile = document.getElementById('dark-mode-text-mobile');
      
      // Get saved dark mode state or default to false
      let isDarkMode = localStorage.getItem(DARK_MODE_STORAGE_KEY) === 'true';
      
      // Apply dark mode class to all screen elements
      function applyDarkMode(darkMode) {
        document.querySelectorAll('.screen').forEach(screen => {
          if (darkMode) {
            screen.classList.add('screen--dark-mode');
          } else {
            screen.classList.remove('screen--dark-mode');
          }
        });
        
        // Update icon visibility for desktop
        if (lightIcon && darkIcon) {
          if (darkMode) {
            lightIcon.classList.add('hidden');
            darkIcon.classList.remove('hidden');
          } else {
            lightIcon.classList.remove('hidden');
            darkIcon.classList.add('hidden');
          }
        }
        
        // Update icon visibility and text for mobile
        if (lightIconMobile && darkIconMobile && darkModeTextMobile) {
          if (darkMode) {
            lightIconMobile.classList.add('hidden');
            darkIconMobile.classList.remove('hidden');
            darkModeTextMobile.textContent = 'Dark Mode';
          } else {
            lightIconMobile.classList.remove('hidden');
            darkIconMobile.classList.add('hidden');
            darkModeTextMobile.textContent = 'Light Mode';
          }
        }
        
        // Update indicator dots visibility
        const darkModeIndicator = document.getElementById('dark-mode-indicator');
        const darkModeIndicatorMobile = document.getElementById('dark-mode-indicator-mobile');
        
        if (darkModeIndicator) {
          if (darkMode) {
            darkModeIndicator.classList.remove('hidden');
          } else {
            darkModeIndicator.classList.add('hidden');
          }
        }
        
        if (darkModeIndicatorMobile) {
          if (darkMode) {
            darkModeIndicatorMobile.classList.remove('hidden');
          } else {
            darkModeIndicatorMobile.classList.add('hidden');
          }
        }
        
        // Update dark mode notices visibility
        document.querySelectorAll('.dark-mode-notice').forEach(notice => {
          if (darkMode) {
            notice.classList.remove('hidden');
          } else {
            notice.classList.add('hidden');
          }
        });
        
        // Save state to localStorage
        localStorage.setItem(DARK_MODE_STORAGE_KEY, darkMode);
      }
      
      // Handle dark mode toggle click
      function toggleDarkMode() {
        isDarkMode = !isDarkMode;
        applyDarkMode(isDarkMode);
        
        // Run terminalize function after dark mode change
        if (typeof terminalize === 'function') {
          terminalize();
        }
      }
      
      // Add event listeners to both desktop and mobile buttons
      if (darkModeToggle) {
        darkModeToggle.addEventListener('click', toggleDarkMode);
        console.log('Desktop dark mode toggle found and event listener added');
      } else {
        console.log('Desktop dark mode toggle NOT found');
      }
      
      if (darkModeToggleMobile) {
        darkModeToggleMobile.addEventListener('click', toggleDarkMode);
        console.log('Mobile dark mode toggle found and event listener added');
      } else {
        console.log('Mobile dark mode toggle NOT found');
      }
      
      // Apply saved dark mode on page load
      applyDarkMode(isDarkMode);
      
      // Re-apply dark mode when new content is loaded (for Turbo)
      document.addEventListener('turbo:load', function() {
        const savedDarkMode = localStorage.getItem(DARK_MODE_STORAGE_KEY) === 'true';
        applyDarkMode(savedDarkMode);
      });
      
      // Re-apply dark mode after Turbo frame updates
      document.addEventListener('turbo:frame-load', function() {
        const savedDarkMode = localStorage.getItem(DARK_MODE_STORAGE_KEY) === 'true';
        applyDarkMode(savedDarkMode);
      });
    }
    
    // Initialize orientation toggle functionality
    function initOrientationToggle() {
      const ORIENTATION_STORAGE_KEY = 'frameworkOrientation';
      const orientationToggle = document.getElementById('orientation-toggle');
      const orientationToggleMobile = document.getElementById('orientation-toggle-mobile');
      const landscapeIcon = document.getElementById('landscape-icon');
      const portraitIcon = document.getElementById('portrait-icon');
      const landscapeIconMobile = document.getElementById('landscape-icon-mobile');
      const portraitIconMobile = document.getElementById('portrait-icon-mobile');
      const orientationTextMobile = document.getElementById('orientation-text-mobile');
      
      // Get saved orientation state or default to false (landscape)
      let isPortrait = localStorage.getItem(ORIENTATION_STORAGE_KEY) === 'true';
      
      // Apply orientation class to all screen elements
      function applyOrientation(portrait) {
        document.querySelectorAll('.screen').forEach(screen => {
          // Remove both orientation classes first
          screen.classList.remove('screen--portrait', 'screen--landscape');
          
          // Add the appropriate orientation class
          if (portrait) {
            screen.classList.add('screen--portrait');
          } else {
            screen.classList.add('screen--landscape');
          }
        });
        
        // Update icon visibility for desktop
        if (landscapeIcon && portraitIcon) {
          if (portrait) {
            landscapeIcon.classList.add('hidden');
            portraitIcon.classList.remove('hidden');
          } else {
            landscapeIcon.classList.remove('hidden');
            portraitIcon.classList.add('hidden');
          }
        }
        
        // Update icon visibility and text for mobile
        if (landscapeIconMobile && portraitIconMobile && orientationTextMobile) {
          if (portrait) {
            landscapeIconMobile.classList.add('hidden');
            portraitIconMobile.classList.remove('hidden');
            orientationTextMobile.textContent = 'Portrait';
          } else {
            landscapeIconMobile.classList.remove('hidden');
            portraitIconMobile.classList.add('hidden');
            orientationTextMobile.textContent = 'Landscape';
          }
        }
        
        // Update indicator dots visibility
        const orientationIndicator = document.getElementById('orientation-indicator');
        const orientationIndicatorMobile = document.getElementById('orientation-indicator-mobile');
        
        if (orientationIndicator) {
          if (portrait) {
            orientationIndicator.classList.remove('hidden');
          } else {
            orientationIndicator.classList.add('hidden');
          }
        }
        
        if (orientationIndicatorMobile) {
          if (portrait) {
            orientationIndicatorMobile.classList.remove('hidden');
          } else {
            orientationIndicatorMobile.classList.add('hidden');
          }
        }
        
        // Save state to localStorage
        localStorage.setItem(ORIENTATION_STORAGE_KEY, portrait);
      }
      
      // Handle orientation toggle click
      function toggleOrientation() {
        // Capture current example position before making changes
        const currentExamplePosition = getCurrentExamplePosition();
        
        isPortrait = !isPortrait;
        applyOrientation(isPortrait);
        
        // Run terminalize function after orientation change
        if (typeof terminalize === 'function') {
          terminalize();
        }
        
        // Restore scroll position after layout settles
        restoreScrollPosition(currentExamplePosition);
      }
      
      // Add event listeners to both desktop and mobile buttons
      if (orientationToggle) {
        orientationToggle.addEventListener('click', toggleOrientation);
        console.log('Desktop orientation toggle found and event listener added');
      } else {
        console.log('Desktop orientation toggle NOT found');
      }
      
      if (orientationToggleMobile) {
        orientationToggleMobile.addEventListener('click', toggleOrientation);
        console.log('Mobile orientation toggle found and event listener added');
      } else {
        console.log('Mobile orientation toggle NOT found');
      }
      
      // Apply saved orientation on page load
      applyOrientation(isPortrait);
      
      // Re-apply orientation when new content is loaded (for Turbo)
      document.addEventListener('turbo:load', function() {
        const savedOrientation = localStorage.getItem(ORIENTATION_STORAGE_KEY) === 'true';
        applyOrientation(savedOrientation);
      });
      
      // Re-apply orientation after Turbo frame updates
      document.addEventListener('turbo:frame-load', function() {
        const savedOrientation = localStorage.getItem(ORIENTATION_STORAGE_KEY) === 'true';
        applyOrientation(savedOrientation);
      });
    }
    
    // Generate table of contents from headings in the main content area
    function generateTableOfContents() {
      // Select content area and TOC container
      const mainContentArea = document.querySelector('#main-content');
      if (!mainContentArea) return;
      
      const headings = mainContentArea.querySelectorAll('h2, h3, h4');
      const tocContents = document.querySelectorAll('.js-toc-content');
      
      if (tocContents.length === 0) return;
      
      // Track used IDs to ensure uniqueness
      const usedIds = new Set();
      
      // Clear loading placeholder in all TOC containers
      tocContents.forEach(tocContent => {
        tocContent.innerHTML = '';
        
        // No headings found
        if (headings.length === 0) {
          tocContent.innerHTML = '<div class="text-gray-400 italic">No sections found</div>';
          return;
        }
        
        // Generate TOC items
        headings.forEach(heading => {
          // Skip headings without IDs or generate unique ones
          if (!heading.id) {
            const headingText = heading.textContent.trim();
            let baseId = headingText.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');
            let uniqueId = baseId;
            let counter = 1;
            
            // Ensure ID uniqueness by adding a counter if needed
            while (usedIds.has(uniqueId)) {
              uniqueId = `${baseId}-${counter}`;
              counter++;
            }
            
            heading.id = uniqueId;
            usedIds.add(uniqueId);
          } else {
            // Track existing IDs to avoid conflicts
            usedIds.add(heading.id);
          }
          
          // Create TOC entry
          const tocEntry = document.createElement('a');
          tocEntry.href = `#${heading.id}`;
          
          // Add appropriate indentation based on heading level
          let indentClass = '';
          if (heading.tagName === 'H3') indentClass = 'pl-2';
          if (heading.tagName === 'H4') indentClass = 'pl-4';
          
          tocEntry.className = `block py-1 transition-colors duration-150 text-gray-600 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 ${indentClass}`;
          tocEntry.textContent = heading.textContent.trim();
          
          // Add click behavior to smooth scroll
          tocEntry.addEventListener('click', function(e) {
            e.preventDefault();
            const targetHeading = document.getElementById(heading.id);
            if (targetHeading) {
              targetHeading.scrollIntoView({ behavior: 'smooth' });
              window.history.pushState(null, null, `#${heading.id}`);
              
              // Remove active class from all links in all TOC containers
              document.querySelectorAll('.js-toc-content a').forEach(link => {
                link.classList.remove('font-bold', 'text-primary-500', 'dark:text-primary-400');
              });
              
              // Add active class to clicked link and corresponding links in other TOC containers
              document.querySelectorAll(`.js-toc-content a[href="#${heading.id}"]`).forEach(link => {
                link.classList.add('font-bold', 'text-primary-500', 'dark:text-primary-400');
              });
              
              // Close mobile sidebars if open
              closeMobileSidebars();
            }
          });
          
          tocContent.appendChild(tocEntry);
        });
      });
      
      // Set up the IntersectionObserver for scrollspy
      setupScrollspy(headings);
      
      // Initial highlight based on hash
      highlightCurrentSection();
    }
    
    // Setup scrollspy to highlight TOC items based on scroll position
    function setupScrollspy(headings) {
      const observerOptions = {
        root: null,
        rootMargin: '0px 0px -70% 0px',
        threshold: 0
      };
      
      const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // Remove active class from all links
            document.querySelectorAll('.js-toc-content a').forEach(link => {
              link.classList.remove('font-bold', 'text-primary-500', 'dark:text-primary-400');
            });
            
            // Add active class to current section link in all TOC containers
            document.querySelectorAll(`.js-toc-content a[href="#${entry.target.id}"]`).forEach(link => {
              link.classList.add('font-bold', 'text-primary-500', 'dark:text-primary-400');
            });
          }
        });
      }, observerOptions);
      
      // Track all sections that have IDs
      headings.forEach(section => {
        observer.observe(section);
      });
    }
    
    // Highlight the current section in TOC based on URL hash
    function highlightCurrentSection() {
      const hash = window.location.hash.substring(1);
      if (hash) {
        document.querySelectorAll(`.js-toc-content a[href="#${hash}"]`).forEach(link => {
          link.classList.add('font-bold', 'text-primary-500', 'dark:text-primary-400');
        });
        
        // Scroll to the element after a short delay to ensure page is fully loaded
        setTimeout(() => {
          const targetElement = document.getElementById(hash);
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      }
    }
    
    // Get the currently most visible example and its position relative to viewport
    function getCurrentExamplePosition() {
      // If user is near the top of the page, don't restore scroll position
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const isNearTop = scrollTop < 200; // Within 200px of the top
      
      if (isNearTop) {
        return null; // Don't restore position when at top of page
      }
      
      const examples = document.querySelectorAll('.screen');
      if (examples.length === 0) return null;
      
      const viewportHeight = window.innerHeight;
      const viewportCenter = scrollTop + (viewportHeight / 2);
      
      let closestExample = null;
      let closestDistance = Infinity;
      let exampleIndex = -1;
      
      // Find the example closest to the center of the viewport
      examples.forEach((example, index) => {
        const rect = example.getBoundingClientRect();
        const exampleTop = scrollTop + rect.top;
        const exampleCenter = exampleTop + (rect.height / 2);
        const distance = Math.abs(exampleCenter - viewportCenter);
        
        if (distance < closestDistance) {
          closestDistance = distance;
          closestExample = example;
          exampleIndex = index;
        }
      });
      
      if (!closestExample) return null;
      
      // Calculate the offset from the top of the viewport to the top of the example
      const exampleRect = closestExample.getBoundingClientRect();
      const offsetFromViewportTop = exampleRect.top;
      
      return {
        index: exampleIndex,
        offsetFromTop: offsetFromViewportTop
      };
    }
    
    // Restore scroll position to maintain the same example at the same relative position
    function restoreScrollPosition(examplePosition) {
      if (!examplePosition) return;
      
      // Use requestAnimationFrame to ensure we restore position as soon as layout is complete
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          const examples = document.querySelectorAll('.screen');
          if (examples.length > examplePosition.index) {
            const targetExample = examples[examplePosition.index];
            const targetRect = targetExample.getBoundingClientRect();
            const currentOffsetFromTop = targetRect.top;
            
            // Calculate how much we need to scroll to restore the original offset
            const scrollAdjustment = currentOffsetFromTop - examplePosition.offsetFromTop;
            const newScrollTop = window.pageYOffset + scrollAdjustment;
            
            // Instantly scroll to the calculated position (no animation)
            window.scrollTo(0, newScrollTop);
          }
        });
      });
    }
    
    // Initialize sticky border radius functionality
    function initStickyBorderRadius() {
      const sentinel = document.getElementById('sticky-sentinel');
      const container = document.getElementById('test-config-container');
      
      if (!sentinel || !container) return;
      
      // Create intersection observer to watch the sentinel element
      const observer = new IntersectionObserver(
        function(entries) {
          entries.forEach(function(entry) {
            if (entry.isIntersecting) {
              // Sentinel is visible, sticky element is not stuck - show rounded corners
              container.classList.remove('rounded-t-none');
              container.classList.add('rounded-t-lg');
            } else {
              // Sentinel is not visible, sticky element is stuck - remove top rounded corners
              container.classList.remove('rounded-t-lg');
              container.classList.add('rounded-t-none');
            }
          });
        },
        {
          root: null,
          rootMargin: '-68px 0px 0px 0px',
          threshold: 0
        }
      );
      
      // Start observing the sentinel
      observer.observe(sentinel);
    }
    
    <% unless params[:action] == 'index' %>
    // Function to show mobile sidebar with animation
    function showMobileSidebar(sidebarId) {
      const sidebar = document.getElementById(sidebarId);
      if (!sidebar) return;
      
      // First make it visible but transparent
      sidebar.classList.remove('hidden');
      // Force a reflow to ensure the transition works
      sidebar.offsetHeight;
      // Then fade in the overlay
      sidebar.classList.remove('opacity-0');
      
      // Slide in the sidebar content
      const sidebarContent = sidebar.querySelector('div');
      if (sidebarContent) {
        sidebarContent.classList.remove(sidebarId === 'left-sidebar-mobile' ? '-translate-x-full' : 'translate-x-full');
      }
      
      document.body.classList.add('overflow-hidden');
    }
    
    // Function to hide mobile sidebar with animation
    function hideMobileSidebar(sidebarId) {
      const sidebar = document.getElementById(sidebarId);
      if (!sidebar) return;
      
      // First fade out the overlay
      sidebar.classList.add('opacity-0');
      
      // Slide out the sidebar content
      const sidebarContent = sidebar.querySelector('div');
      if (sidebarContent) {
        sidebarContent.classList.add(sidebarId === 'left-sidebar-mobile' ? '-translate-x-full' : 'translate-x-full');
      }
      
      // Wait for the animation to complete before hiding
      setTimeout(() => {
        sidebar.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
      }, 300);
    }
    
    // Initialize sidebar functionality
    function initSidebars() {
      // Constants for sidebar widths - Centralized configuration
      const SIDEBAR_WIDTH_CLASS = 'w-64'; // 16rem width for both sidebars
      const COLLAPSED_CLASS = 'w-0';

      // Get saved states from localStorage or set defaults
      const savedLeftState = localStorage.getItem('leftSidebarVisible');
      const savedRightState = localStorage.getItem('rightSidebarVisible');
      
      // Default states based on saved preferences
      let leftSidebarVisible = savedLeftState !== null ? savedLeftState === 'true' : true;
      let rightSidebarVisible = savedRightState !== null ? savedRightState === 'true' : true;
      
      // Elements
      const leftSidebar = document.getElementById('left-sidebar');
      const rightSidebar = document.getElementById('right-sidebar');
      const leftSidebarMobile = document.getElementById('left-sidebar-mobile');
      const rightSidebarMobile = document.getElementById('right-sidebar-mobile');
      const toggleLeftBtn = document.getElementById('toggle-left-sidebar');
      const toggleRightBtn = document.getElementById('toggle-right-sidebar');
      const closeBtns = document.querySelectorAll('[data-sidebar-close]');
      
      // Function to check if we're at medium breakpoint (md but not lg)
      function isMediumBreakpoint() {
        return window.innerWidth >= 768 && window.innerWidth < 1024;
      }
      
      // Function to check if we should use mobile or desktop sidebar
      function isMobileView() {
        return window.innerWidth < 768;
      }
      
      // Function to close mobile sidebars
      function closeMobileSidebars() {
        if (leftSidebarMobile) leftSidebarMobile.classList.add('hidden');
        if (rightSidebarMobile) rightSidebarMobile.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
      }
      
      // Make closeMobileSidebars globally available
      window.closeMobileSidebars = closeMobileSidebars;
      
      // Function to update desktop sidebar visibility - MODIFIED to handle initial load
      function updateDesktopLayout(isInitialLoad = false) {
        const isMobile = isMobileView();
        
        // Only apply to desktop sidebars when in desktop mode
        if (!isMobile) {
          if (leftSidebar) {
            if (leftSidebarVisible) {
              leftSidebar.classList.add(SIDEBAR_WIDTH_CLASS);
              leftSidebar.classList.remove(COLLAPSED_CLASS);
              if (toggleLeftBtn) toggleLeftBtn.classList.add('bg-gray-200', 'dark:bg-gray-800');
            } else {
              leftSidebar.classList.remove(SIDEBAR_WIDTH_CLASS);
              leftSidebar.classList.add(COLLAPSED_CLASS);
              if (toggleLeftBtn) toggleLeftBtn.classList.remove('bg-gray-200', 'dark:bg-gray-800');
            }
          }
          
          if (rightSidebar) {
            // Always collapse the right sidebar at medium breakpoint
            if (isMediumBreakpoint()) {
              rightSidebar.classList.remove(SIDEBAR_WIDTH_CLASS);
              rightSidebar.classList.add(COLLAPSED_CLASS);
              if (toggleRightBtn) toggleRightBtn.classList.remove('bg-gray-200', 'dark:bg-gray-800');
            } else if (rightSidebarVisible) {
              rightSidebar.classList.add(SIDEBAR_WIDTH_CLASS);
              rightSidebar.classList.remove(COLLAPSED_CLASS);
              if (toggleRightBtn) toggleRightBtn.classList.add('bg-gray-200', 'dark:bg-gray-800');
            } else {
              rightSidebar.classList.remove(SIDEBAR_WIDTH_CLASS);
              rightSidebar.classList.add(COLLAPSED_CLASS);
              if (toggleRightBtn) toggleRightBtn.classList.remove('bg-gray-200', 'dark:bg-gray-800');
            }
          }
        }
        
        // Enable transitions after initial layout is set
        if (isInitialLoad) {
          // Add transition classes after a small delay to ensure the DOM has updated
          setTimeout(() => {
            if (leftSidebar) {
              leftSidebar.classList.add('transition-all', 'duration-300');
            }
            if (rightSidebar) {
              rightSidebar.classList.add('transition-all', 'duration-300');
            }
          }, 50);
        }
        
        // Save states to localStorage
        localStorage.setItem('leftSidebarVisible', leftSidebarVisible);
        localStorage.setItem('rightSidebarVisible', rightSidebarVisible);
      }
      
      // Toggle left sidebar
      if (toggleLeftBtn) {
        toggleLeftBtn.addEventListener('click', function() {
          if (isMobileView()) {
            // Mobile: show overlay with animation
            showMobileSidebar('left-sidebar-mobile');
          } else {
            // Desktop: toggle visibility
            leftSidebarVisible = !leftSidebarVisible;
            updateDesktopLayout(false);
          }
        });
      }
      
      // Toggle right sidebar
      if (toggleRightBtn) {
        toggleRightBtn.addEventListener('click', function() {
          // At medium breakpoint, show mobile sidebar instead
          if (isMediumBreakpoint()) {
            showMobileSidebar('right-sidebar-mobile');
            return;
          }
          
          if (isMobileView()) {
            // Mobile: show overlay with animation
            showMobileSidebar('right-sidebar-mobile');
          } else {
            // Desktop: toggle visibility
            rightSidebarVisible = !rightSidebarVisible;
            updateDesktopLayout(false);
          }
        });
      }
      
      // Close buttons for mobile sidebars
      closeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const sidebarType = btn.getAttribute('data-sidebar-close');
          hideMobileSidebar(`${sidebarType}-sidebar-mobile`);
        });
      });
      
      // Close mobile sidebars when clicking outside
      document.querySelectorAll('.mobile-sidebar').forEach(sidebar => {
        sidebar.addEventListener('click', function(e) {
          if (e.target === sidebar) {
            const sidebarId = sidebar.id;
            hideMobileSidebar(sidebarId);
          }
        });
      });
      
      // Handle window resize to update layout appropriately
      window.addEventListener('resize', function() {
        updateDesktopLayout(false);
      });
      
      // Initial layout setup with transitions disabled
      updateDesktopLayout(true);
    }
    <% end %>
  </script>
</body>
</html> 