<% meta title: t('.edit_device') %>

<div class="<%= layout_narrow_classes %>">
  <%= form_for(@device, html: {autocomplete: 'off'}) do |f| %>
    <div class="<%= layout_title_classes %>">
      <div class="w-full">
        <div class="flex py-4">
          <div class="flex flex-grow items-center">
            <div class="flex flex-col">
              <h2 class="<%= title_h2_classes %>"><%= @device.name %></h2>
              <p class="<%= title_p_classes %>"><%= @device.friendly_id %></p>
            </div>
          </div>
          <div class="shrink-0 flex justify-end items-end">
            <button type="submit" class="<%= button_regular_classes %> <%= button_primary_classes %> ml-3"><%= t(:save) %></button>
          </div>
        </div>
      </div>
    </div>

    <div class="<%= layout_single_col_classes %>">
      <div class="<%= card_classes %> mb-6 divide-y divide-gray-200 dark:divide-gray-700">
        <div class="p-6">
          <label for="device_name" class="<%= field_label_classes %>"><%= t('.device_name') %></label>
          <p class="<%= field_description_classes %>"><%= t('.device_name_hint') %></p>
          <%= f.text_field :name, class: field_input_classes %>
        </div>

        <div class="p-6">
          <label for="device_friendly_id" class="<%= field_label_classes %>"><%= t(:device_id) %></label>
          <p class="<%= field_description_classes %>"><%= t('.your_device') %> <%= t(:device_id) %></p>
          <input type="text" class="<%= field_input_classes %>" name="" readonly value="<%= @device.friendly_id %>">
        </div>

        <div class="p-6">
          <label for="device_model" class="<%= field_label_classes %>"><%= t('.device_model') %></label>
          <p class="<%= field_description_classes %>"><%= t('.device_model_hint') %></p>
          <% if @device.byod? %>
            <%= f.select :model_id, options_for_select(byod_device_options, selected: @device.model_id), {}, { class: field_input_classes } %>
            <small class="<%= field_help_text_classes %>"><%= string_with_link(t('.device_model_switch_warning'), 'https://help.usetrmnl.com/en/articles/11547008-device-switcher-faq', class: 'underline', target: :_blank) %>.</small>
          <% else %>
            <input type="text" class="<%= field_input_classes %>" name="" readonly value="<%= @device.model.name %>">
          <% end %>
        </div>

        <div class="p-6">
          <label for="device_special_function" class="<%= field_label_classes %>"><%= t('.special_function') %></label>
          <p class="<%= field_description_classes %>"><%= t('.special_function_hint') %></p>
          <%= f.select :special_function, options_for_select(Device::SPECIAL_FUNCTIONS.invert, selected: @device.special_function), {}, { onchange: 'setupGuestModeOptions()', class: field_input_classes } %>
          <small class="<%= field_help_text_classes %>"><%= string_with_link(t('.special_function_learn_more'), 'https://help.usetrmnl.com/en/articles/9672080-special-functions', class: 'underline', target: :_blank) %></small>
        </div>

        <div id="guest_mode_options" class="p-6 hidden">
          <label class="<%= field_label_classes %>"><%= t('.guest_mode') %></label>
          <p class="<%= field_description_classes %>"><%= t('.guest_mode_hint') %></p>

          <div class="flex items-center my-2">
            <%= f.select :guest_mode_item_id, options_for_select(playlist_item_option_names(@playlist_items), selected: @device.guest_mode_item_id), { include_blank: t('.guest_mode_item_placeholder') }, { class: field_input_small_classes } %>
            <span class="px-1 <%= field_description_classes %>">for</span>
            <%= f.select :guest_mode_item_duration, options_for_select(guest_mode_duration_options, selected: @device.guest_mode_item_duration), { include_blank: t('.guest_mode_duration_placeholder') }, { class: field_input_small_classes } %>
          </div>
        </div>
      </div>
    </div>

    <div class="<%= layout_single_col_classes %>">
      <div class="<%= card_classes %> mb-6 divide-y divide-gray-200 dark:divide-gray-700">
        <div class="p-6">
          <label class="<%= field_label_classes %>"><%= t('.battery_consumption') %></label>
          <p class="<%= field_description_classes %>"><%= t(:charge_level) %>: <%= @device.percent_charged %>%</p>
          <div class="bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 my-3">
            <div class="bg-primary-500 h-2.5 rounded-full" title="<%= @device.percent_charged %>" style="width: <%= @device.percent_charged %>%"></div>
          </div>
        </div>

        <div class="p-6">
          <div class="flex">
            <label class="<%= field_label_classes %>"><%= t('.low_battery_email_notification') %></label>
            <div class="flex-1 text-right">
              <label class="inline-flex items-center cursor-pointer">
                <%= f.check_box :low_battery_notification_enabled, class: 'sr-only peer' %>
                <div class="relative w-11 h-6 bg-gray-400 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-500"></div>
              </label>
            </div>
          </div>
          <p class="<%= field_description_classes %>">Get notified via email when your device battery is running low.</p>
        </div>

        <div class="p-6">
          <div class="flex">
            <label class="<%= field_label_classes %>"><%= t('.sleep_mode') %></label>
            <div class="flex-1 text-right">
              <label class="inline-flex items-center cursor-pointer">
                <%= f.check_box :sleep_mode_enabled, class: 'sr-only peer', onchange: 'setupSleepScreenOptions(this.checked, true)' %>
                <div class="relative w-11 h-6 bg-gray-400 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-500"></div>
              </label>
            </div>
          </div>
          <p class="<%= field_description_classes %>">
            <%= t('.battery_consumption_hint') %>
            <%= "<b>Set your time zone #{link_to('here', account_index_path(anchor: 'time_zone'), class: 'underline')} to enable this feature.</b>".html_safe if current_user.tz.nil? %>
          </p>

          <div class="flex items-center my-2">
            <%= select_tag 'device[sleep_start_time]', options_for_select(Device::CONFIGURABLE_TIMES.invert, selected: @device.sleep_start_time), { class: field_input_small_classes } %>
            <span class="px-1 <%= field_description_classes %>">to</span>
            <%= select_tag 'device[sleep_end_time]', options_for_select(Device::CONFIGURABLE_TIMES.invert, selected: @device.sleep_end_time), { class: field_input_small_classes } %>
          </div>
        </div>

        <div class="p-6">
          <div class="flex">
            <label class="<%= field_label_classes %>"><%= t('.sleep_screen') %></label>
            <div class="flex-1 text-right relative" id="sleep-screen-toggle-container">
              <label class="inline-flex items-center cursor-pointer" id="sleep-screen-label"<%= ' data-tooltip-target="sleep-screen-tooltip"'.html_safe unless @device.sleep_mode_enabled? %>>
                <%= f.check_box :sleep_screen_enabled, class: 'sr-only peer', id: 'device_sleep_screen_enabled' %>
                <div class="relative w-11 h-6 bg-gray-400 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-500"></div>
              </label>
              <div id="sleep-screen-tooltip" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700 right-0 bottom-full mb-2<%= ' hidden' if @device.sleep_mode_enabled? %>">
                Enable Sleep Mode to use this feature
                <div class="tooltip-arrow" data-popper-arrow></div>
              </div>
            </div>
          </div>
          <p class="<%= field_description_classes %>">Display a sleep screen during sleep mode hours.</p>
        </div>

        <div class="p-6">
          <label class="<%= field_label_classes %>">Refresh Rate</label>
          <p class="<%= field_description_classes %>"><%= t('.sleep_mode_hint') %></p>

          <div class="relative mb-10">
            <%= f.number_field :refresh_interval, type: 'range', min: 300, max: 3600, class: "w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700" %>
            <span class="text-sm text-gray-500 dark:text-gray-400 absolute start-0 -bottom-10 cursor-pointer" onclick="setRefreshInterval(300)">5 <%= t(:mins) %><br>
              (<span id="refresh_interval_5_mins">30</span> <%= t(:days) %>)</span>
            <span class="text-sm text-gray-500 dark:text-gray-400 absolute start-1/4 -translate-x-1/2 rtl:translate-x-1/2 -bottom-10 cursor-pointer hidden md:block" onclick="setRefreshInterval(900)">15 <%= t(:mins) %><br>
              (<span id="refresh_interval_15_mins">80</span> <%= t(:days) %>)</span>
            <span class="text-sm text-gray-500 dark:text-gray-400 absolute start-2/4 -translate-x-1/2 rtl:translate-x-1/2 -bottom-10 cursor-pointer" onclick="setRefreshInterval(1800)">30 <%= t(:mins) %><br>
              (<span id="refresh_interval_30_mins">160</span> <%= t(:days) %>)</span>
            <span class="text-sm text-gray-500 dark:text-gray-400 absolute start-3/4 -translate-x-1/2 rtl:translate-x-1/2 -bottom-10 cursor-pointer hidden md:block" onclick="setRefreshInterval(2700)">45 <%= t(:mins) %><br>
              (<span id="refresh_interval_45_mins">200</span>+ <%= t(:days) %>)</span>
            <span class="text-sm text-gray-500 dark:text-gray-400 absolute end-0 -bottom-10 cursor-pointer" onclick="setRefreshInterval(3600)">60 <%= t(:mins) %><br>
              (<span id="refresh_interval_60_mins">400</span>+ <%= t(:days) %>)</span>
          </div>
        </div>
      </div>
    </div>

    <div class="<%= layout_single_col_classes %>">
      <div class="<%= card_classes %> mb-6 divide-y divide-gray-200 dark:divide-gray-700">
        <div class="p-6">
          <div class="flex">
            <div>
              <label class="<%= field_label_classes %>"><%= t('.developer_perks') %></label>

              <% unless @device.developer_edition_unlocked? %>
                <p class="<%= field_description_classes %>">
                  <%= string_with_link(t('.developer_perks_hint'), 'https://usetrmnl.com/blog/developer-edition', class: 'underline', target: :_blank) %>
                </p>
              <% end %>
            </div>

            <% if @device.discord_access_unlocked? %>
              <div class="text-right flex-1">
                <%= link_to Rails.application.credentials.discord_server_invite_url, title: "Join our developer-only server", target: :_blank do %>
                  <%= image_tag 'discord-server.png', class: "w-20 ml-auto" %>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <div class="p-6">
          <div class="flex">
            <label class="<%= field_label_classes %>"><%= t('.accepts_beta_firmware') %></label>
            <div class="flex-1 text-right">
              <label class="inline-flex items-center cursor-pointer">
                <% enabled = @device.developer_edition_unlocked? %>
                <%= f.check_box :accepts_beta_firmware, class: 'sr-only peer', disabled: !enabled %>
                <div class="relative w-11 h-6 bg-gray-400 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-500"></div>
                <% onclick = @device.developer_edition_unlocked? ? nil : "alert('#{t(:developer_edition_required)}')" %>
                <span onclick="<%= onclick %>" class="sr-only"><%= t('.accepts_beta_firmware') %></span>
              </label>
            </div>
          </div>
          <p class="<%= field_description_classes %>"><%= t('.accepts_beta_firmware_hint') %></p>
          <p class="<%= field_description_classes %>">
            Current: <%= @device.firmware_version %> | Stable: <%= Firmware.stable_release&.version %> | Staging: <%= Firmware.staging_release&.version %> | <%= link_to 'View on GitHub', 'https://github.com/usetrmnl/firmware', target: :_blank, class: 'underline' %>
          </p>
        </div>

        <div class="p-6">
          <div class="flex">
            <label class="<%= field_label_classes %>"><%= t('.ota_enabled') %></label>
            <div class="flex-1 text-right">
              <label class="inline-flex items-center cursor-pointer">
                <% enabled = @device.developer_edition_unlocked? %>
                <%= f.check_box :ota_enabled, class: 'sr-only peer', disabled: !enabled %>
                <div class="relative w-11 h-6 bg-gray-400 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-500"></div>
                <% onclick = @device.developer_edition_unlocked? ? nil : "alert('#{t(:developer_edition_required)}')" %>
                <span onclick="<%= onclick %>" class="sr-only"><%= t('.ota_enabled') %></span>
              </label>
            </div>
          </div>
          <p class="<%= field_description_classes %>"><%= t('.ota_enabled_hint') %></p>
        </div>

        <div class="p-6">
          <label class="<%= field_label_classes %>"><%= t('.device_credentials') %></label>
          <div class="mt-6 space-y-6">
            <% mac_address = @device.developer_edition_unlocked? ? @device.mac_address : 'XX:XX:XX:XX:XX' %>
            <% api_key = @device.developer_edition_unlocked? ? @device.api_key : '••••••••••••••••••' %>

            <% if @device.byod? %>
              <div>
                <label for="device_mac_address" class="<%= field_label_classes %> !mb-1 text-sm">MAC Address</label>
                <%= f.text_field :mac_address, class: "#{field_input_classes} !px-2 !py-2.5 !min-h-0 !mt-0" %>
              </div>
            <% else %>
              <%= render "shared/click_to_copy", label: 'MAC Address', object_id: @device.id, value: mac_address, type: 'macAddress' %>
            <% end %>
            <%= render "shared/click_to_copy", label: 'API Key', object_id: @device.id, value: api_key, type: 'apiKey' %>
          </div>
          <p class="<%= field_description_classes %> mt-3">
            <%= string_with_link(t('.device_credentials_hint'), 'https://docs.usetrmnl.com/go/private-api/introduction', class: 'underline', target: :_blank) %>
          </p>
        </div>

        <div class="p-6">
          <label class="<%= field_label_classes %>"><%= t('.logs') %></label>
          <p class="<%= field_description_classes %>"><%= t('.logs_hint') %></p>
          <div class="mt-3">
            <%= link_to t('.logs'), device_logs_path(device_id: @device.id), class: "#{button_regular_classes} #{button_secondary_classes}" %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="<%= layout_single_col_classes %>">
    <div class="<%= card_classes %> mb-6 divide-y divide-gray-200 dark:divide-gray-700">
      <div class="p-6">
        <label for="device_visibility" class="<%= field_label_classes %>"><%= t('.visibility') %></label>
        <p class="<%= field_description_classes %>"><%= t('.visibility_hint') %></p>
        <%= select_tag "device[visibility]", options_for_select(Device::visibilities.map { |k, v| [ k.capitalize, k ] }, @device.visibility), { class: field_input_classes, form: "edit_device_#{@device.id}" } %>
      </div>

      <%= form_for(@device, url: mirror_device_path(@device.id), html: { autocomplete: 'off', method: :post }) do |f| %>
        <div class="p-6">
          <label for="user_first_name" class="<%= field_label_classes %>"><%= t('.mirror_device') %></label>
          <p class="<%= field_description_classes %>"><%= t('.mirror_device_hint') %></p>
          <%= f.text_field :mirror, { placeholder: '716AE5', class: field_input_classes } %>
          <div class="mt-3">
            <% if @device.mirror.present? %>
              <%= link_to t('.mirror_sync'), resync_mirror_device_path(@device.id), data: { turbo_method: :post }, class: "#{button_regular_classes} #{button_gray_light_classes} mt-3" %>
              <%= link_to t('.mirror_stop'), stop_mirror_device_path(@device.id), data: { turbo_method: :post }, class: "#{button_regular_classes} #{button_secondary_classes}" %>
            <% else %>
              <%= f.submit t('.mirror'), class: "#{button_regular_classes} #{button_secondary_classes}" %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <div class="<%= layout_single_col_classes %>">
    <div class="<%= card_classes %> mb-6">
      <div class="p-6">
        <label class="<%= field_label_classes %>"><%= t('.identify_device') %></label>
        <p class="<%= field_description_classes %>"><%= t('.identify_device_hint') %></p>
        <%= button_to t('.identify'), identify_device_path(@device.id), method: :post, data: { turbo: true }, class: "#{button_regular_classes} #{button_secondary_classes} mt-3" %>
      </div>
    </div>
  </div>

  <% if @device.physical? %>
    <div class="<%= layout_single_col_classes %>">
      <div class="<%= card_classes %> mb-6">
        <div class="p-6">
          <label for="user_first_name" class="<%= field_label_classes %>"><%= t('.unlink_device') %></label>
          <p class="<%= field_description_classes %>"><%= t('.unlink_device_hint') %> <%= string_with_link(t('.unlink_device_learn_more'), 'https://help.usetrmnl.com/en/articles/11546838-how-to-unpair-re-pair-a-device', class: 'underline', target: :_blank) %></p>
          <%= link_to t('.unlink'), dissociate_device_path(@device.id), data: { turbo_confirm: t('.unlink_confirm'), turbo_method: :post }, class: "#{button_regular_classes} #{button_secondary_classes} mt-3" %>
        </div>
      </div>
    </div>
  <% end %>
</div>

<script>
  function setRefreshInterval(seconds) {
    device_refresh_interval.value = seconds;
  }

  function setupSleepScreenOptions(sleepModeEnabled, isUserInteraction = false) {
    const sleepScreenToggle = document.getElementById('device_sleep_screen_enabled');
    const sleepScreenLabel = document.getElementById('sleep-screen-label');
    const sleepScreenTooltip = document.getElementById('sleep-screen-tooltip');

    if (sleepScreenToggle && sleepScreenLabel && sleepScreenTooltip) {
      if (!sleepModeEnabled) {
        // Disable sleep screen when sleep mode is turned off
        sleepScreenToggle.checked = false;
        sleepScreenToggle.disabled = true;
        // Show tooltip
        sleepScreenLabel.setAttribute('data-tooltip-target', 'sleep-screen-tooltip');
        sleepScreenTooltip.classList.remove('hidden');
      } else {
        // Enable sleep screen when sleep mode is enabled
        if (isUserInteraction) {
          // Only auto-enable sleep screen when user manually turns on sleep mode
          sleepScreenToggle.checked = true;
        }
        sleepScreenToggle.disabled = false;
        // Hide tooltip
        sleepScreenLabel.removeAttribute('data-tooltip-target');
        sleepScreenTooltip.classList.add('hidden');
      }
    }

    let minIntervals = {
      5: 30,
      15: 80,
      30: 160,
      45: 240,
      60: 320
    }

    Object.entries(minIntervals).forEach(([int, estimate]) => {
      let newEstimate = sleepModeEnabled ? (estimate * 1.33) : estimate;
      window[`refresh_interval_${int}_mins`].innerText = Math.round(newEstimate);
    })
  }

  function setupGuestModeOptions() {
     const guestModeOptions = document.getElementById('guest_mode_options');
     const specialFunctionSelect = document.getElementById('device_special_function');
     const guestModeItemSelect = document.getElementById('device_guest_mode_item_id');
     const guestModeDurationSelect = document.getElementById('device_guest_mode_item_duration');

     if (specialFunctionSelect.value == 'guest_mode') {
       guestModeOptions.classList.remove('hidden');
       guestModeItemSelect.required = true;
       guestModeDurationSelect.required = true;
     } else {
       guestModeOptions.classList.add('hidden');
       guestModeItemSelect.required = false;
       guestModeDurationSelect.required = false;
     }
  }

  document.addEventListener('DOMContentLoaded', function () {
    setupSleepScreenOptions(<%= @device.sleep_mode_enabled %>);
    setupGuestModeOptions();
  });
</script>
