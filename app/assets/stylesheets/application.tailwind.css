@import url('https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@import "actiontext.css";
@import "trix.css";
@import "codemirror.css";

.contain-paint {
    contain: paint;
}

.scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
}

/* playlists#index */

#playlistGroupsContainer {
    counter-reset: playlist-counter;
}

.playlist-item {
    counter-increment: playlist-counter;
}

.playlist-item .playlist-counter::before {
    content: counter(playlist-counter);
}

.sortable-chosen .playlist-item .playlist-counter::before {
    opacity: 0;
}

.playlist-item__ghost {
    opacity: 0.1;
}

/* user-facing logs */
.pagy {
    justify-content: center;

    @apply flex space-x-1 font-semibold text-sm text-gray-500;

    a:not(.gap) {
        @apply block rounded-lg px-3 py-1 bg-gray-200;
        &:hover {
            @apply bg-gray-300;
        }
        &:not([href]) {
            /* disabled links */
            @apply text-gray-300 bg-gray-100 cursor-default;
        }
        &.current {
            @apply text-white bg-primary-500 dark:bg-black;
        }
    }

    label {
        @apply inline-block whitespace-nowrap bg-gray-200 rounded-lg px-3 py-0.5;

        input {
            @apply bg-gray-100 border-none rounded-md;
        }
    }
}


input[type="range"]::-webkit-slider-thumb {
  background: #f8654b;
  appearance: none;
  cursor: pointer;
  border: 0;
  border-radius: 9999px;
  width: 1rem;
  height: 1rem;
}

@layer utilities {
    .scrollbar-hide {
        /* Firefox */
        scrollbar-width: none;
        /* Safari and Chrome */
        &::-webkit-scrollbar {
            display: none;
        }
        /* For when actually scrolling - show a subtle scrollbar */
        &:hover {
            /* Firefox */
            scrollbar-width: thin;
            /* Safari and Chrome */
            &::-webkit-scrollbar {
                display: block;
                width: 8px;
                height: 8px;
            }
            &::-webkit-scrollbar-track {
                background: transparent;
            }
            &::-webkit-scrollbar-thumb {
                background-color: rgba(155, 155, 155, 0.5);
                border-radius: 20px;
                border: transparent;
            }
        }
    }
}

/* Guides */
.guide h1 {
    @apply font-heading text-4xl lg:text-5xl tracking-tight mb-4;
}

.guide h2 {
    @apply font-heading text-2xl lg:text-3xl tracking-tight mb-4 pt-8;
    position: relative;
}
.guide h2::after {
    content: "";
    @apply block w-full h-px absolute left-1/2 translate-x-[-50%] -bottom-2 bg-gray-400;
}

.guide h3 {
    @apply font-heading text-xl lg:text-2xl tracking-tight pt-4 mb-2;
}

.guide p {
    @apply text-base mb-4;
}
.guide img {
    @apply block rounded bg-gray-200 my-4 lg:my-6;
}

.guide pre {
    @apply bg-gray-300 rounded-md px-1 py-0.5;
}

.guide code,
.guide pre {
    @apply bg-gray-300 rounded-md px-1 py-0.5;
}

.guide ul {
    @apply list-disc ml-4 mb-4;
}

.guide ol {
    @apply list-decimal ml-4 mb-4;
}

.guide li {
    @apply list-disc ml-4;
}

.guide a {
    @apply text-primary-500 underline;
}

.guide a:hover {
    @apply text-primary-700 underline;
}

.guide strong {
    @apply font-bold;
}

.guide blockquote {
    @apply bg-gray-300 rounded-md px-1 py-0.5;
}
