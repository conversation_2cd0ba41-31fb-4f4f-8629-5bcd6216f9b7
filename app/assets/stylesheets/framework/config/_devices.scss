// ============================================
// TRMNL Framework - Device Configurations
// ============================================
//
// DO NOT MANUALLY EDIT THIS FILE!
//
// Make changes to app/models/concerns/device_model_specs.rb, then run:
//
//     rake css:sync
//

$devices: (
    'og': (
        'screen-w': 800px,
        'screen-h': 480px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 1,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'v2': (
        'screen-w': 1040px,
        'screen-h': 780px,
        'pixel-ratio': 1.8,
        'ui-scale': 1.0,
        'color-depth': 4,
        'gap-scale': 1.0,
        'size': 'lg'
    ),
    'og_png': (
        'screen-w': 800px,
        'screen-h': 480px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 1,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'amazon_kindle_2024': (
        'screen-w': 600px,
        'screen-h': 444px,
        'pixel-ratio': 2.414,
        'ui-scale': 0.8,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'sm'
    ),
    'amazon_kindle_paperwhite_6th_gen': (
        'screen-w': 1024px,
        'screen-h': 768px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'amazon_kindle_paperwhite_7th_gen': (
        'screen-w': 1448px,
        'screen-h': 1072px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'inkplate_10': (
        'screen-w': 1200px,
        'screen-h': 820px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 3,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'amazon_kindle_7': (
        'screen-w': 800px,
        'screen-h': 600px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'mac_classic': (
        'screen-w': 512px,
        'screen-h': 342px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 1,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'playdate': (
        'screen-w': 400px,
        'screen-h': 240px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 1,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'inky_impression_7_3': (
        'screen-w': 800px,
        'screen-h': 480px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'kobo_libra_2': (
        'screen-w': 1680px,
        'screen-h': 1264px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'lg'
    ),
    'amazon_kindle_oasis_2': (
        'screen-w': 1680px,
        'screen-h': 1264px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'lg'
    ),
    'ogv2': (
        'screen-w': 800px,
        'screen-h': 480px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 2,
        'gap-scale': 1.0,
        'size': 'md'
    ),
    'kobo_aura_one': (
        'screen-w': 1872px,
        'screen-h': 1404px,
        'pixel-ratio': 1.0,
        'ui-scale': 1.0,
        'color-depth': 8,
        'gap-scale': 1.0,
        'size': 'lg'
    ),
);
