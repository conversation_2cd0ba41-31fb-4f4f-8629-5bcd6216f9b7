module Plugins
  class Notion < Base
    class ApiClient
      API_VERSION = '2022-06-28'.freeze
      BASE_URL = 'https://api.notion.com'.freeze
      SEARCH_URL = "#{BASE_URL}/v1/search".freeze
      DATABASE_QUERY_URL = "#{BASE_URL}/v1/databases".freeze
      PAGE_URL = "#{BASE_URL}/v1/pages".freeze
      BLOCKS_URL = "#{BASE_URL}/v1/blocks".freeze

      def initialize(access_token)
        @access_token = access_token
      end

      def list_databases
        search_objects('database', page_size: 100, &method(:format_list_item))
      end

      def list_pages
        search_objects('page', page_size: 100, &method(:format_list_item))
      end

      def search_databases(query = "")
        search_objects('database', query: query, page_size: 100, &method(:format_search_item))
      end

      def search_pages(query = "")
        search_objects('page', query: query, page_size: 100, &method(:format_search_item))
      end

      def search_objects(object_type, query: nil, page_size: 50, &)
        body = {
          filter: { property: 'object', value: object_type },
          page_size: page_size
        }
        body[:query] = query if query.present?

        response = make_request(:post, SEARCH_URL, body: body, error_context: "searching #{object_type}s")
        return [] unless response

        results = response['results'] || []
        results.map(&)
      end

      def query_database(notion_database_id, page_size: 20, sorts: [])
        url = "#{DATABASE_QUERY_URL}/#{notion_database_id}/query"
        body = { page_size: page_size, sorts: sorts }
        make_request(:post, url, body: body, error_context: "querying database #{notion_database_id}")
      end

      def get_database_info(notion_database_id)
        url = "#{DATABASE_QUERY_URL}/#{notion_database_id}"
        make_request(:get, url, error_context: "getting database info #{notion_database_id}")
      end

      def get_page_info(notion_page_id)
        url = "#{PAGE_URL}/#{notion_page_id}"
        make_request(:get, url, error_context: "getting page info #{notion_page_id}")
      end

      def get_page_blocks(notion_page_id, page_size: 20)
        url = "#{BLOCKS_URL}/#{notion_page_id}/children?page_size=#{page_size}"
        make_request(:get, url, error_context: "getting page blocks #{notion_page_id}")
      end

      private

      def make_request(method, url, body: nil, error_context: "API request")
        options = { headers: headers }
        options[:body] = body.to_json if body

        response = HTTParty.send(method, url, options)
        return nil unless response&.success?

        parsed = response.parsed_response
        parsed.is_a?(String) ? JSON.parse(parsed) : parsed
      rescue StandardError => e
        Rails.logger.error "Notion API error #{error_context}: #{e.message}"
        nil
      end

      def format_list_item(item)
        title = extract_title(item)
        { title => item['id'] }
      end

      def format_search_item(item)
        title = extract_title(item)
        { id: item['id'], name: title, text: title }
      end

      def extract_title(item)
        if item['title'] # Database
          extract_title_from_data(item['title']) || "Untitled Database"
        else # Page
          extract_page_title(item['properties']) || "Untitled Page"
        end
      end

      def headers
        {
          'Authorization' => "Bearer #{@access_token}",
          'Notion-Version' => API_VERSION,
          'Content-Type' => 'application/json'
        }
      end

      def extract_page_title(properties)
        return nil unless properties

        title_data = find_title_data_in_properties(properties)
        extract_title_from_data(title_data)
      end

      def extract_title_from_data(title_data)
        return nil unless title_data.is_a?(Array)

        title_data.map { |part| part['plain_text'] || part.dig('text', 'content') }.join.presence
      end

      def find_title_data_in_properties(properties)
        # Try common title field names first
        %w[Name Title name title].each do |key|
          title_data = properties[key]&.dig('title')
          return title_data if title_data
        end

        # Fall back to finding any title type property
        properties.each_value do |value|
          return value['title'] if value['type'] == 'title' && value['title']
        end

        nil
      end

    end
  end
end
