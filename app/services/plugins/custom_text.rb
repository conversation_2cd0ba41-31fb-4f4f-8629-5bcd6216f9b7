module Plugins
  class CustomText < Base

    def locals
      { phrase:, font_scaling:, text_alignment:, title_bar_text: }
    end

    private

    def phrase
      phrases.sample
    end

    def phrases
      settings['phrases'].split(';').map(&:strip)
    end

    def font_scaling
      settings['font_scaling'] || 'fixed'
    end

    def text_alignment
      settings['text_alignment'] || 'center'
    end

    def title_bar_text
      settings['title_bar_text'] || ''
    end
  end
end
