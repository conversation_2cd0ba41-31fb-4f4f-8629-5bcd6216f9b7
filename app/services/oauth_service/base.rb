# frozen_string_literal: true

module OauthService
  class Base
    attr_reader :credentials, :plugin_keyname

    def initialize(plugin_keyname)
      @plugin_keyname = plugin_keyname.to_s
      @credentials = Rails.application.credentials.plugins[plugin_keyname.to_sym]
    end

    def authorization_url
      oauth_client.auth_code.authorize_url(
        redirect_uri: redirect_uri,
        **authorization_params
      )
    end

    def process_callback(code, user)
      tokens = fetch_access_token(code)
      store_credentials(tokens, user)
    end

    def fetch_access_token(code)
      token = oauth_client.auth_code.get_token(
        code,
        redirect_uri: redirect_uri,
        **token_params
      )

      extract_token_data(token)
    end

    def refresh_access_token(refresh_token, user)
      token = oauth_client.get_token(
        refresh_token,
        grant_type: 'refresh_token',
        **refresh_token_params
      )

      new_tokens = extract_token_data(token)
      store_credentials(new_tokens, user)
      new_tokens
    end

    def store_credentials(tokens, user)
      credentials = user.credentials.deep_dup
      updated_credentials = credentials.merge(plugin_keyname => tokens)

      unless user.update(credentials: updated_credentials)
        raise "Failed to store OAuth credentials: #{user.errors.full_messages.join(', ')}"
      end
    end

    protected

    # Override these in subclasses
    def oauth_site
      raise NotImplementedError, "#{self.class} must implement oauth_site"
    end

    def oauth_authorize_url
      raise NotImplementedError, "#{self.class} must implement oauth_authorize_url"
    end

    def oauth_token_url
      raise NotImplementedError, "#{self.class} must implement oauth_token_url"
    end

    def oauth_client_options
      {
        site: oauth_site,
        authorize_url: oauth_authorize_url,
        token_url: oauth_token_url
      }
    end

    def authorization_params
      {}
    end

    def token_params
      {}
    end

    def refresh_token_params
      {}
    end

    def extract_token_data(token)
      {
        'access_token' => token.token,
        'refresh_token' => token.refresh_token,
        'expires_at' => token.expires_at
      }.compact
    end

    private

    def oauth_client
      ::OAuth2::Client.new(
        credentials[:client_id],
        credentials[:client_secret],
        oauth_client_options
      )
    end

    def redirect_uri
      "#{Rails.application.credentials.base_url}/plugin_settings/#{plugin_keyname}/redirect"
    end
  end
end
