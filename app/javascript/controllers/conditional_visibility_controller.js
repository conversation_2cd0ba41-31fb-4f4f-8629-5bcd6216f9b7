import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["field"]
  static values = { rules: Array }

  connect() {
    // Apply visibility rules on initial page load based on saved field values
    this.applyVisibilityRules()
  }

  fieldChanged() {
    // Re-apply visibility rules when any controlling field changes
    this.applyVisibilityRules()
  }

  applyVisibilityRules() {
    // Process each visibility rule
    this.rulesValue.forEach(rule => {
      // Find the controlling field by searching for an input/select with the field name
      const controllingField = this.element.querySelector(
        `[name*="[${rule.field}]"], [name*="[encrypted_settings][${rule.field}]"]`
      )
      
      if (!controllingField) return

      // Get the current value of the controlling field
      const currentValue = controllingField.value

      // Find the condition that matches the current value
      const matchingCondition = rule.conditions.find(condition => condition.when === currentValue)

      // Collect all fields that could be hidden by any condition
      const allPossiblyHiddenFields = rule.conditions
        .flatMap(condition => condition.hidden || [])
        .filter((field, index, self) => self.indexOf(field) === index) // unique values

      // Determine which fields should be hidden for the current value
      const fieldsToHide = matchingCondition ? (matchingCondition.hidden || []) : []

      // Update visibility for all possibly affected fields
      allPossiblyHiddenFields.forEach(fieldName => {
        const shouldHide = fieldsToHide.includes(fieldName)
        this.updateFieldVisibility(fieldName, !shouldHide)
      })
    })
  }

  updateFieldVisibility(fieldName, visible) {
    // Find the field wrapper by matching the data-field-name attribute
    const fieldWrapper = this.fieldTargets.find(element => 
      element.dataset.fieldName === fieldName
    )

    if (fieldWrapper) {
      // Use the hidden class to hide/show the field
      if (visible) {
        fieldWrapper.classList.remove('hidden')
      } else {
        fieldWrapper.classList.add('hidden')
      }
    }
  }
}