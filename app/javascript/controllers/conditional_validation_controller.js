import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["field"]
  static values = {
    rules: Object
  }

  connect() {
    this.validateFields()
  }

  validateFields() {
    const fieldsWithRules = this.fieldTargets.filter(field => {
      return field.dataset.conditionalValidationRules
    })

    fieldsWithRules.forEach(field => {
      const rules = JSON.parse(field.dataset.conditionalValidationRules)
      this.applyValidationRules(field, rules)
    })
  }

  applyValidationRules(field, rules) {
    const controllingField = this.element.querySelector(`[name*="${rules.field}"]`)
    if (!controllingField) return

    const controllingValue = controllingField.value
    const condition = rules.conditions.find(cond => cond.when === controllingValue)

    const previouslyAddedValidationAddedFields = document.querySelectorAll(".border-red-500");

    previouslyAddedValidationAddedFields.forEach((field) => {
      field.classList.remove("border-red-500")
      field.required = false;
    })

    if (condition) {
      const requiredFields = condition.required || []
      requiredFields.forEach(fieldName => {
        const targetField = document.querySelector(`[name*="[${fieldName}]"]`)
        if (targetField && !targetField.value.trim()) {
          targetField.classList.add("border-red-500")
          targetField.required = true;
        }
      })
    }
  }
}
