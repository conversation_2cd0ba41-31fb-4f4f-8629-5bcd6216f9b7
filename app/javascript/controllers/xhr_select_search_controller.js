import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = {
    url: String,
    function: String,
    valueField: String,
    labelField: String,
    placeholder: String,
    name: String,
    initialValue: String
  }

  static targets = ["input", "dropdown", "hiddenInput"]

  get valueField() {
    return this.valueFieldValue || "id"
  }
  get labelField() {
    return this.labelFieldValue || "name"
  }

  getOptionValue(option) {
    return option[this.valueField]
  }
  getOptionLabel(option) {
    return option[this.labelField] || option.name || option.text || this.getOptionValue(option)
  }

  connect() {
    this.debounceTimeouts = new Set() // Track timeout IDs for cleanup
    this.initializeState()
    this.initializeFromStoredValue()
    this.setupDebounce()
  }

  disconnect() {
    // Clear any pending timeouts to prevent memory leaks
    this.debounceTimeouts.forEach((timeoutId) => clearTimeout(timeoutId))
    this.debounceTimeouts.clear()
  }

  initializeState() {
    this.isOpen = false
    this.selectedIndex = -1
    this.options = []
    this.selectedOption = null
  }

  setupDebounce() {
    // Create a debounced version of the search method
    this.debouncedSearch = this.debounce(this._performSearch.bind(this), 300)
  }

  initializeFromStoredValue() {
    if (!this.initialValueValue || typeof this.initialValueValue !== "string") return

    if (!this.initialValueValue.includes("::")) {
      // Handle case where value doesn't contain :: (simple ID format)
      this.selectedOption = { [this.valueField]: this.initialValueValue, [this.labelField]: this.initialValueValue }
      this.inputTarget.value = this.initialValueValue
      this.hiddenInputTarget.value = this.initialValueValue
      return
    }

    // Parse stored format: "id::name"
    const parts = this.initialValueValue.split("::")
    if (parts.length < 2) {
      console.warn("Invalid format for stored value:", this.initialValueValue)
      return
    }

    const [id, ...nameParts] = parts
    const name = nameParts.join("::") // Handle names that might contain ::

    if (!id) {
      console.warn("Invalid ID in stored value:", this.initialValueValue)
      return
    }

    this.selectedOption = { [this.valueField]: id, [this.labelField]: name || id }
    this.inputTarget.value = name || id
    this.hiddenInputTarget.value = this.initialValueValue
  }

  // Stimulus action methods - called from HTML data-action attributes
  search(event) {
    this.debouncedSearch(event)
  }

  async _performSearch(event) {
    const query = event.target.value

    // Clear selection if user types something different
    if (this.selectedOption && query !== this.getSelectedLabel()) {
      this.clearSelection()
    }

    const results = await this.fetchData(query)
    this.updateDropdown(results)
    this.showDropdown()
  }

  getSelectedLabel() {
    return this.selectedOption ? this.getOptionLabel(this.selectedOption) : ""
  }

  handleKeyboard(event) {
    const keyHandlers = {
      "ArrowDown": () => (this.isOpen ? this.moveSelection(1) : this.showDropdown()),
      "ArrowUp": () => this.isOpen && this.moveSelection(-1),
      "Enter": () => this.handleEnterKey(),
      "Escape": () => this.isOpen && this.hideDropdown()
    }

    const handler = keyHandlers[event.key]
    if (handler && handler()) {
      event.preventDefault()
    }
  }

  moveSelection(direction) {
    const newIndex = this.selectedIndex + direction
    this.selectedIndex = Math.max(-1, Math.min(newIndex, this.options.length - 1))
    this.updateHighlight()
    this.scrollToSelected()
    return true
  }

  handleEnterKey() {
    if (!this.isOpen) {
      this.showDropdown()
      return true
    }

    if (this.selectedIndex >= 0) {
      this.selectOption(this.options[this.selectedIndex])
      return true
    }

    return false
  }

  scrollToSelected() {
    if (this.selectedIndex < 0) return

    const selectedElement = this.dropdownTarget.querySelector(`[data-index="${this.selectedIndex}"]`)
    if (selectedElement) {
      selectedElement.scrollIntoView({
        block: "nearest",
        behavior: "instant"
      })
    }
  }

  async fetchData(query) {
    const form = this.element.closest("form")
    const pluginSettingId = form.querySelector('input[name*="[id]"]')?.value

    const params = {
      function: this.functionValue,
      query: query,
      plugin_setting: { id: pluginSettingId }
    }

    // Include other form values for context
    form.querySelectorAll("input[type='text'], select, input[type='hidden']").forEach((input) => {
      if (input.name && input.value && input !== this.hiddenInputTarget) {
        let name = this.normalizeFieldName(input.name)
        params.plugin_setting[name] = input.value
      }
    })

    try {
      const response = await fetch(this.urlValue, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(params)
      })

      return response.ok ? await response.json() : []
    } catch (error) {
      console.error("Search failed:", error)
      return []
    }
  }

  updateDropdown(options) {
    this.options = Array.isArray(options) ? options : []
    this.selectedIndex = -1
    this.dropdownTarget.innerHTML = ""

    if (this.options.length === 0) {
      this.dropdownTarget.appendChild(this.createNoResultsElement())
      return
    }

    this.options.forEach((option, index) => {
      this.dropdownTarget.appendChild(this.createOptionElement(option, index))
    })
  }

  createNoResultsElement() {
    const element = document.createElement("div")
    element.className = "px-4 py-3 text-sm text-center text-gray-500 dark:text-gray-400"
    element.textContent = "No results found"
    return element
  }

  createOptionElement(option, index) {
    const element = document.createElement("div")
    element.className =
      "px-4 py-3 text-sm cursor-pointer border-b border-gray-100 last:border-b-0 text-black dark:text-white dark:border-gray-700 transition-colors duration-150 ease-in-out hover:bg-gray-50 dark:hover:bg-gray-700"
    element.textContent = this.getOptionLabel(option)
    element.setAttribute("data-index", index.toString())

    element.addEventListener("mousedown", (e) => {
      e.preventDefault()
      this.selectOption(this.options[parseInt(e.target.dataset.index)])
    })

    return element
  }

  selectOption(option) {
    this.selectedOption = option
    const label = this.getOptionLabel(option)
    const value = this.getOptionValue(option)

    this.inputTarget.value = label
    this.hiddenInputTarget.value = `${value}::${label}`

    this.hideDropdown()
    this.hiddenInputTarget.dispatchEvent(new Event("change", { bubbles: true }))
  }

  clearSelection() {
    this.selectedOption = null
    this.hiddenInputTarget.value = ""
    this.inputTarget.value = ""
  }

  showDropdown() {
    this.setDropdownState(true)
  }

  hideDropdown() {
    // Use setTimeout to allow option selection to happen before hiding
    setTimeout(() => {
      this.setDropdownState(false)
      this.selectedIndex = -1
      this.updateHighlight()
    }, 150)
  }

  setDropdownState(isOpen) {
    this.isOpen = isOpen
    if (isOpen) {
      this.dropdownTarget.classList.remove("hidden")
      this.dropdownTarget.classList.add("block")
    } else {
      this.dropdownTarget.classList.remove("block")
      this.dropdownTarget.classList.add("hidden")
    }
  }

  updateHighlight() {
    // First clear all highlights by removing background classes
    this.dropdownTarget.querySelectorAll("[data-index]").forEach((option) => {
      option.classList.remove("bg-gray-50", "dark:bg-gray-700")
    })

    // Then apply highlight to selected option using Tailwind classes
    if (this.selectedIndex >= 0) {
      const selectedElement = this.dropdownTarget.querySelector(`[data-index="${this.selectedIndex}"]`)
      if (selectedElement) {
        selectedElement.classList.add("bg-gray-50", "dark:bg-gray-700")
      }
    }
  }

  normalizeFieldName(fieldName) {
    // Remove 'plugin_setting' prefix and convert nested brackets to underscores
    // e.g., "plugin_setting[foo][bar]" -> "foo_bar"
    return fieldName
      .replace(/^plugin_setting/, "")
      .split(/[\[\]]+/)
      .filter((part) => part.length > 0)
      .join("_")
  }

  debounce(func, wait) {
    let timeout
    return (...args) => {
      if (timeout) {
        clearTimeout(timeout)
        this.debounceTimeouts.delete(timeout)
      }
      timeout = setTimeout(() => {
        this.debounceTimeouts.delete(timeout)
        func.apply(this, args)
      }, wait)
      this.debounceTimeouts.add(timeout)
    }
  }
}
