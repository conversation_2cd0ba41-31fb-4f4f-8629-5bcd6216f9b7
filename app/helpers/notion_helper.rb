module NotionHelper
  # Filters properties by names and returns only those with present values
  # @param properties [Array] Array of property hashes with :name and :value keys
  # @param property_names [Array] Array of property names to match
  # @return [Array] Array of matching properties with present values
  def filter_properties_by_names(properties, property_names)
    return [] if properties.blank? || property_names.blank?

    properties_map = properties.each_with_object({}) do |prop, hash|
      hash[prop[:name]&.downcase] = prop if prop[:name]
    end

    property_names.filter_map do |name|
      prop = properties_map[name.downcase]
      prop if prop && prop[:value].present?
    end
  end
end
