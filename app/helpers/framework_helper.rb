# rubocop:disable Metrics/ModuleLength
module FrameworkHelper
  def framework_pages
    %w[screen background image border rounded view layout title_bar columns grid title description
       label value table chart item progress gap clamp overflow format_value fit_value content_limiter flex size responsive text spacing mashup pixel_perfect rich_text text_stroke image_stroke visibility divider scale aspect_ratio responsive_test]
  end

  def current_page_index
    framework_pages.index(params[:action])
  end

  def previous_page
    return nil if current_page_index.nil? || current_page_index.zero?

    framework_pages[current_page_index - 1]
  end

  def next_page
    return nil if current_page_index.nil? || current_page_index == framework_pages.length - 1

    framework_pages[current_page_index + 1]
  end

  def framework_device_configs
    models = current_user&.admin? ? DeviceModel.all_model_specs : DeviceModel.visible_model_specs

    models.to_h do |model|
      [model[:keyname], {
        name: model[:name],
        bitDepth: model.dig(:css, :bit_depth),
        size: model.dig(:css, :size)
      }]
    end.as_json
  end

  def framework_device_options
    models = current_user&.admin? ? DeviceModel.all_model_specs : DeviceModel.visible_model_specs

    models.map do |model|
      [model[:name], model[:keyname]]
    end.sort
  end

  def framework_bit_depth_options
    models = current_user&.admin? ? DeviceModel.all_model_specs : DeviceModel.visible_model_specs

    models.map { |model| model[:colour_depth] }.uniq.sort.map do |bit_depth|
      ["#{bit_depth}-bit", "#{bit_depth}bit"]
    end
  end

  def page_titles
    {
      'screen' => 'Screen',
      'background' => 'Background',
      'border' => 'Border',
      'spacing' => 'Spacing',
      'gap' => 'Gap',
      'size' => 'Size',
      'image' => 'Image',
      'view' => 'View',
      'layout' => 'Layout',
      'title_bar' => 'Title Bar',
      'columns' => 'Columns',
      'grid' => 'Grid',
      'flex' => 'Flex',
      'text' => 'Text',
      'rich_text' => 'Rich Text',
      'title' => 'Title',
      'description' => 'Description',
      'label' => 'Label',
      'value' => 'Value',
      'clamp' => 'Clamp',
      'item' => 'Item',
      'table' => 'Table',
      'chart' => 'Chart',
      'overflow' => 'Overflow',
      'format_value' => 'Format Value',
      'fit_value' => 'Fit Value',
      'content_limiter' => 'Content Limiter',
      'mashup' => 'Mashup',
      'pixel_perfect' => 'Pixel Perfect',
      'text_stroke' => 'Text Stroke',
      'image_stroke' => 'Image Stroke',
      'visibility' => 'Visibility',
      'responsive' => 'Responsive',
      'responsive_test' => 'Responsive Test',
      'rounded' => 'Rounded',
      'divider' => 'Divider',
      'progress' => 'Progress',
      'scale' => 'Scale',
      'aspect_ratio' => 'Aspect Ratio'
    }
  end

  def page_description(page)
    descriptions = {
      'screen' => 'Device screen dimensions, orientation, and display properties',
      'background' => 'Grayscale dithered patterns optimized for 1-bit rendering',
      'border' => 'Apply border patterns that create the illusion of different border intensities',
      'spacing' => 'Control element spacing with fixed margin and padding values',
      'gap' => 'Set precise spacing between elements with predefined gap values',
      'size' => 'Define exact width and height dimensions for elements',
      'image' => 'Optimize images using dithering techniques for 1-bit rendering',
      'view' => 'Show your plugin in different sizes with Mashup view containers',
      'layout' => 'Primary container for organizing plugin content',
      'title_bar' => 'Standardized title bar with plugin information and instance details',
      'columns' => 'Implement zero-config column layouts for content organization',
      'grid' => 'Create grid layouts with predefined column structures',
      'flex' => 'Arrange elements with flexible layouts and alignment options',
      'text' => 'Control text color, alignment and formatting',
      'rich_text' => 'Display formatted paragraphs with alignment and size variants',
      'title' => 'Style headings with consistent typography',
      'description' => 'Format descriptive text with standardized styles',
      'label' => 'Create clear labels for unified content identification',
      'value' => 'Display data values with consistent formatting',
      'clamp' => 'Manage text overflow with single and multi-line truncation',
      'item' => 'Build standardized list items and content blocks',
      'table' => 'Create data tables optimized for 1-bit rendering',
      'chart' => 'Visualize data optimized for 1-bit rendering',
      'overflow' => 'Handle content overflow in fixed-size containers',
      'format_value' => 'Format numbers and values with consistent styling',
      'fit_value' => 'Automatically resize numbers and values to fit within their containers',
      'content_limiter' => 'Change font size when content overflows to fit within the container',
      'mashup' => 'Assemble multiple plugin views into a single interface',
      'pixel_perfect' => 'Ensure text renders with crisp edges by aligning to the pixel grid',
      'text_stroke' => 'Legible text when displayed on shaded backgrounds',
      'image_stroke' => 'Legible images when displayed on shaded backgrounds',
      'visibility' => 'Control element visibility based on display bit depth',
      'responsive' => 'Adapt styles based on screen width using breakpoint prefixes',
      'responsive_test' => 'Test responsive utilities and compare SCSS mixins with CSS classes',
      'rounded' => 'Control element rounding with predefined values',
      'divider' => 'Create horizontal or vertical dividers between elements',
      'progress' => 'Display progress bars in different styles',
      'scale' => 'Scale interface to affect content density and readability',
      'aspect_ratio' => 'Maintain consistent proportions for elements regardless of their content'
    }
    descriptions[page] || ''
  end

  def page_icon(page, classes = nil)
    render partial: "shared/icons/#{page}", locals: { classes: classes }
  rescue ActionView::MissingTemplate
    # Fallback to info icon if specific icon doesn't exist
    render partial: "shared/icons/info", locals: { classes: classes }
  end

  # Helper to create headings with automatic IDs for TOC
  def framework_heading(level, text, additional_classes = '')
    # Track used IDs to ensure uniqueness within a single page render
    @framework_heading_ids ||= Set.new

    base_id = text.parameterize
    unique_id = base_id
    counter = 1

    # Ensure ID uniqueness by adding a counter if needed
    while @framework_heading_ids.include?(unique_id)
      unique_id = "#{base_id}-#{counter}"
      counter += 1
    end

    @framework_heading_ids.add(unique_id)

    tag_class = case level
                when 1
                  framework_page_title_classes
                when 2
                  framework_section_title_classes
                when 3
                  framework_subsection_title_classes
                when 4
                  framework_subsubsection_title_classes
                else
                  ''
                end

    content_tag(:div, class: 'group relative') do
      top = 4 - level # add/remove a pixel to vertically center
      content_tag('a', '#', href: anchored_framework_path(unique_id), class: 'inline hidden group-hover:block text-primary-500 cursor-pointer', style: "position: absolute; left: -16px; padding-right: 8px; top: #{top}px") +
        content_tag("h#{level}", text, id: unique_id, class: "inline #{tag_class} #{additional_classes}")
    end
  end

  def anchored_framework_path(id)
    "#{Rails.application.credentials.base_url}#{request.path}##{id}"
  end

  def device_selector_should_refresh?
    # Framework pages that require page refresh instead of terminalize()
    refresh_pages = %w[overflow scale]

    params[:controller] == 'framework' && refresh_pages.include?(params[:action])
  end

  # Demo content for overflow management examples
  # rubocop:disable Metrics/MethodLength
  def overflow_demo_content
    {
      basic_tasks: [
        {
          index: 1,
          title: "Team Meeting",
          description: "Weekly team sync-up",
          time: "9:00 AM - 10:00 AM",
          status: "Confirmed"
        },
        {
          index: 2,
          title: "Client Presentation",
          description: "Quarterly review with XYZ Corp",
          time: "2:00 PM - 3:30 PM",
          status: "Tentative"
        },
        {
          index: 3,
          title: "Project Deadline",
          description: "Submit final deliverables for Project Alpha",
          time: "11:59 PM",
          status: "Important"
        },
        {
          index: 4,
          title: "Code Review",
          description: "Review pull requests for Project Beta",
          time: "3:30 PM - 4:30 PM",
          status: "High Priority"
        }
      ],

      extended_tasks: [
        {
          index: 1,
          title: "Team Meeting",
          description: "Weekly team sync-up",
          time: "9:00 AM - 10:00 AM",
          status: "Confirmed"
        },
        {
          index: 2,
          title: "Client Presentation",
          description: "Quarterly review with XYZ Corp",
          time: "2:00 PM - 3:30 PM",
          status: "Tentative"
        },
        {
          index: 3,
          title: "Project Deadline",
          description: "Submit final deliverables for Project Alpha",
          time: "11:59 PM",
          status: "Important"
        },
        {
          index: 4,
          title: "Code Review",
          description: "Review pull requests for Project Beta",
          time: "3:30 PM - 4:30 PM",
          status: "High Priority"
        },
        {
          index: 5,
          title: "Database Backup",
          description: "Perform weekly database maintenance",
          time: "6:00 PM - 7:00 PM",
          status: "Automated"
        },
        {
          index: 6,
          title: "Security Audit",
          description: "Monthly security assessment",
          time: "10:00 AM - 12:00 PM",
          status: "Critical"
        },
        {
          index: 7,
          title: "Performance Review",
          description: "Quarterly team performance evaluation",
          time: "1:00 PM - 2:30 PM",
          status: "Scheduled"
        },
        {
          index: 8,
          title: "System Update",
          description: "Apply latest security patches",
          time: "11:00 PM - 12:00 AM",
          status: "Maintenance"
        },
        {
          index: 9,
          title: "Training Session",
          description: "New employee onboarding",
          time: "9:30 AM - 11:30 AM",
          status: "Required"
        },
        {
          index: 10,
          title: "Budget Planning",
          description: "Q4 financial planning meeting",
          time: "3:00 PM - 5:00 PM",
          status: "Planning"
        },
        {
          index: 11,
          title: "Vendor Meeting",
          description: "Quarterly vendor review",
          time: "2:00 PM - 3:00 PM",
          status: "Business"
        },
        {
          index: 12,
          title: "Documentation Update",
          description: "Update project documentation",
          time: "4:00 PM - 5:30 PM",
          status: "Documentation"
        },
        {
          index: 13,
          title: "Infrastructure Check",
          description: "Monitor server health and performance",
          time: "8:00 AM - 9:00 AM",
          status: "Monitoring"
        },
        {
          index: 14,
          title: "Coffee Chat",
          description: "Informal team bonding session",
          time: "10:15 AM - 10:45 AM",
          status: "Social"
        },
        {
          index: 15,
          title: "API Testing",
          description: "Validate new API endpoints",
          time: "11:00 AM - 12:30 PM",
          status: "Testing"
        },
        {
          index: 16,
          title: "Customer Feedback Review",
          description: "Analyze user feedback and feature requests",
          time: "1:30 PM - 2:30 PM",
          status: "Research"
        },
        {
          index: 17,
          title: "Sprint Planning",
          description: "Plan upcoming development sprint",
          time: "3:00 PM - 4:00 PM",
          status: "Planning"
        },
        {
          index: 18,
          title: "Deployment Pipeline",
          description: "Set up automated deployment process",
          time: "4:30 PM - 6:00 PM",
          status: "DevOps"
        },
        {
          index: 19,
          title: "Learning Session",
          description: "Technology deep-dive presentation",
          time: "5:00 PM - 6:00 PM",
          status: "Education"
        },
        {
          index: 20,
          title: "Retrospective",
          description: "Team reflection on past sprint",
          time: "6:00 PM - 7:00 PM",
          status: "Review"
        },
        {
          index: 21,
          title: "Mobile App Testing",
          description: "Cross-platform compatibility testing",
          time: "7:30 PM - 8:30 PM",
          status: "QA"
        },
        {
          index: 22,
          title: "Data Analytics",
          description: "Generate monthly usage reports",
          time: "9:00 PM - 10:00 PM",
          status: "Analytics"
        },
        {
          index: 23,
          title: "Emergency Response",
          description: "On-call incident response protocol",
          time: "12:00 AM - 12:00 AM",
          status: "Emergency"
        },
        {
          index: 24,
          title: "Testing",
          description: "Unit and integration testing",
          time: "4:00 PM - 5:30 PM",
          status: "Confirmed"
        }
      ],

      development_tasks: [
        {
          index: 1,
          title: "Morning Standup",
          description: "Daily team synchronization",
          time: "9:00 AM - 9:30 AM",
          status: "Daily"
        },
        {
          index: 2,
          title: "Sprint Planning",
          description: "Plan upcoming development sprint",
          time: "10:30 AM - 12:00 PM",
          status: "Planning"
        },
        {
          index: 3,
          title: "Code Review",
          description: "Review pending pull requests",
          time: "2:00 PM - 3:00 PM",
          status: "Review"
        },
        {
          index: 4,
          title: "Team Retrospective",
          description: "Reflect on sprint outcomes",
          time: "4:00 PM - 5:00 PM",
          status: "Retrospective"
        },
        {
          index: 5,
          title: "Architecture Review",
          description: "Technical design discussion",
          time: "3:30 PM - 4:30 PM",
          status: "Technical"
        },
        {
          index: 6,
          title: "Client Demo",
          description: "Showcase new features to stakeholders",
          time: "1:00 PM - 2:00 PM",
          status: "Demo"
        }
      ],

      operations_tasks: [
        {
          index: 1,
          title: "Database Migration",
          description: "Update production schema",
          time: "11:00 PM - 12:00 AM",
          status: "Off-hours"
        },
        {
          index: 2,
          title: "Security Patch",
          description: "Apply latest security updates",
          time: "11:00 PM - 11:30 PM",
          status: "Security"
        },
        {
          index: 3,
          title: "Load Testing",
          description: "Performance validation under load",
          time: "6:00 PM - 7:00 PM",
          status: "Testing"
        },
        {
          index: 4,
          title: "Backup Verification",
          description: "Validate backup integrity",
          time: "12:00 AM - 12:30 AM",
          status: "Maintenance"
        },
        {
          index: 5,
          title: "Log Analysis",
          description: "Review system logs for anomalies",
          time: "8:00 AM - 9:00 AM",
          status: "Monitoring"
        },
        {
          index: 6,
          title: "Monitoring Setup",
          description: "Configure new alerting rules",
          time: "5:00 PM - 6:00 PM",
          status: "Configuration"
        }
      ],

      business_tasks: [
        {
          index: 1,
          title: "Budget Review",
          description: "Q4 financial planning session",
          time: "10:00 AM - 11:00 AM",
          status: "Financial"
        },
        {
          index: 2,
          title: "Vendor Assessment",
          description: "Evaluate third-party service providers",
          time: "2:30 PM - 3:30 PM",
          status: "Procurement"
        },
        {
          index: 3,
          title: "Contract Renewal",
          description: "Negotiate terms with key partners",
          time: "4:00 PM - 5:00 PM",
          status: "Legal"
        },
        {
          index: 4,
          title: "Market Analysis",
          description: "Research competitive landscape",
          time: "1:00 PM - 2:00 PM",
          status: "Research"
        },
        {
          index: 5,
          title: "Compliance Audit",
          description: "Annual regulatory compliance check",
          time: "9:00 AM - 10:00 AM",
          status: "Compliance"
        }
      ],

      hr_tasks: [
        {
          index: 1,
          title: "Employee Training",
          description: "New technology workshop",
          time: "11:00 AM - 12:00 PM",
          status: "Training"
        },
        {
          index: 2,
          title: "Performance Reviews",
          description: "Annual employee evaluations",
          time: "3:00 PM - 4:00 PM",
          status: "HR"
        },
        {
          index: 3,
          title: "Team Building",
          description: "Quarterly team bonding activity",
          time: "5:00 PM - 7:00 PM",
          status: "Social"
        },
        {
          index: 4,
          title: "Office Renovation",
          description: "Workspace improvement planning",
          time: "12:00 PM - 1:00 PM",
          status: "Facilities"
        },
        {
          index: 5,
          title: "Safety Drill",
          description: "Emergency evacuation practice",
          time: "2:00 PM - 2:30 PM",
          status: "Safety"
        }
      ],

      # Grouped data for Group Headers examples
      grouped_tasks: {
        today: [
          {
            index: 1,
            title: "Morning Meeting",
            description: "Team sync and updates",
            time: "9:00 AM - 9:30 AM",
            status: "Daily"
          },
          {
            index: 2,
            title: "Code Review",
            description: "Review feature branch",
            time: "10:30 AM - 11:30 AM",
            status: "Review"
          },
          {
            index: 3,
            title: "Lunch Break",
            description: "Team lunch at downtown",
            time: "12:30 PM - 1:30 PM",
            status: "Break"
          },
          {
            index: 4,
            title: "Status Report",
            description: "Weekly progress summary",
            time: "4:00 PM - 4:30 PM",
            status: "Reporting"
          }
        ],
        tomorrow: [
          {
            index: 1,
            title: "Sprint Planning",
            description: "Plan next two weeks",
            time: "10:00 AM - 12:00 PM",
            status: "Planning"
          },
          {
            index: 2,
            title: "Client Demo",
            description: "Show new features",
            time: "2:00 PM - 3:00 PM",
            status: "Demo"
          },
          {
            index: 3,
            title: "Architecture Review",
            description: "Discuss system design",
            time: "4:00 PM - 5:00 PM",
            status: "Technical"
          }
        ],
        this_week: [
          {
            index: 1,
            title: "Security Audit",
            description: "Quarterly security review",
            time: "Wednesday",
            status: "Security"
          },
          {
            index: 2,
            title: "Team Retrospective",
            description: "Sprint reflection",
            time: "Friday",
            status: "Retrospective"
          },
          {
            index: 3,
            title: "Performance Testing",
            description: "Load test new features",
            time: "Thursday",
            status: "Testing"
          }
        ]
      },

      # Extended grouped data for the overflow example
      extended_grouped_tasks: {
        today: [
          {
            index: 1,
            title: "Morning Meeting",
            description: "Team sync and updates",
            time: "9:00 AM - 9:30 AM",
            status: "Daily"
          },
          {
            index: 2,
            title: "Code Review",
            description: "Review feature branch",
            time: "10:30 AM - 11:30 AM",
            status: "Review"
          },
          {
            index: 3,
            title: "Lunch Break",
            description: "Team lunch at downtown",
            time: "12:30 PM - 1:30 PM",
            status: "Break"
          },
          {
            index: 4,
            title: "Client Call",
            description: "Weekly check-in with stakeholders",
            time: "2:00 PM - 3:00 PM",
            status: "Client"
          },
          {
            index: 5,
            title: "Bug Triage",
            description: "Prioritize reported issues",
            time: "3:30 PM - 4:30 PM",
            status: "Bugs"
          },
          {
            index: 6,
            title: "Documentation Update",
            description: "Update API documentation",
            time: "4:30 PM - 5:30 PM",
            status: "Docs"
          },
          {
            index: 7,
            title: "End of Day Sync",
            description: "Review progress and blockers",
            time: "5:30 PM - 6:00 PM",
            status: "Sync"
          }
        ],
        tomorrow: [
          {
            index: 1,
            title: "Sprint Planning",
            description: "Plan next two weeks",
            time: "10:00 AM - 12:00 PM",
            status: "Planning"
          },
          {
            index: 2,
            title: "Client Demo",
            description: "Show new features",
            time: "2:00 PM - 3:00 PM",
            status: "Demo"
          },
          {
            index: 3,
            title: "Architecture Review",
            description: "Discuss system design",
            time: "4:00 PM - 5:00 PM",
            status: "Technical"
          },
          {
            index: 4,
            title: "Stakeholder Meeting",
            description: "Project milestone review",
            time: "9:00 AM - 10:00 AM",
            status: "Stakeholder"
          },
          {
            index: 5,
            title: "QA Testing",
            description: "Regression test suite",
            time: "1:00 PM - 2:00 PM",
            status: "QA"
          },
          {
            index: 6,
            title: "Design Workshop",
            description: "UI/UX design session",
            time: "3:00 PM - 4:00 PM",
            status: "Design"
          },
          {
            index: 7,
            title: "Technical Debt Review",
            description: "Assess code quality issues",
            time: "5:00 PM - 6:00 PM",
            status: "Technical"
          }
        ],
        this_week: [
          {
            index: 1,
            title: "Security Audit",
            description: "Quarterly security review",
            time: "Wednesday",
            status: "Security"
          },
          {
            index: 2,
            title: "Team Retrospective",
            description: "Sprint reflection",
            time: "Friday",
            status: "Retrospective"
          },
          {
            index: 3,
            title: "Performance Testing",
            description: "Load test new features",
            time: "Thursday",
            status: "Testing"
          },
          {
            index: 4,
            title: "Database Optimization",
            description: "Query performance tuning",
            time: "Wednesday",
            status: "Database"
          },
          {
            index: 5,
            title: "Code Cleanup",
            description: "Refactor legacy modules",
            time: "Friday",
            status: "Refactor"
          },
          {
            index: 6,
            title: "Deployment Prep",
            description: "Prepare release candidate",
            time: "Friday",
            status: "Deploy"
          },
          {
            index: 7,
            title: "Knowledge Transfer",
            description: "Onboard new team member",
            time: "Thursday",
            status: "Training"
          },
          {
            index: 8,
            title: "Sprint Review",
            description: "Demo completed work",
            time: "Friday",
            status: "Review"
          },
          {
            index: 9,
            title: "API Documentation",
            description: "Update endpoint specifications",
            time: "Wednesday",
            status: "Documentation"
          },
          {
            index: 10,
            title: "User Testing",
            description: "Conduct usability studies",
            time: "Thursday",
            status: "UX Research"
          },
          {
            index: 11,
            title: "Infrastructure Review",
            description: "Assess server capacity needs",
            time: "Monday",
            status: "Infrastructure"
          },
          {
            index: 12,
            title: "Bug Prioritization",
            description: "Triage reported issues",
            time: "Tuesday",
            status: "Bug Triage"
          },
          {
            index: 13,
            title: "Feature Planning",
            description: "Roadmap discussion for Q2",
            time: "Thursday",
            status: "Planning"
          },
          {
            index: 14,
            title: "Third-party Integration",
            description: "Implement payment gateway",
            time: "Wednesday",
            status: "Integration"
          },
          {
            index: 15,
            title: "Accessibility Audit",
            description: "WCAG compliance review",
            time: "Friday",
            status: "Accessibility"
          },
          {
            index: 16,
            title: "Monitoring Setup",
            description: "Configure alerting systems",
            time: "Tuesday",
            status: "DevOps"
          },
          {
            index: 17,
            title: "Data Migration",
            description: "Move legacy customer data",
            time: "Weekend",
            status: "Migration"
          },
          {
            index: 18,
            title: "License Renewal",
            description: "Update software licenses",
            time: "Monday",
            status: "Legal"
          },
          {
            index: 19,
            title: "Team Training",
            description: "New framework workshop",
            time: "Friday",
            status: "Education"
          },
          {
            index: 20,
            title: "Release Notes",
            description: "Document feature changes",
            time: "Thursday",
            status: "Communication"
          }
        ]
      }
    }
  end
  # rubocop:enable Metrics/MethodLength

  # Render a task item with consistent structure
  def render_task_item(task)
    content_tag(:div, class: 'item') do
      content_tag(:div, class: 'meta') do
        content_tag(:span, task[:index], class: 'index')
      end +
        content_tag(:div, class: 'content') do
          content_tag(:span, task[:title], class: 'title title--small') +
            content_tag(:span, task[:description], class: 'description') +
            content_tag(:div, class: 'flex gap--small') do
              content_tag(:span, task[:time], class: 'label label--small label--underline') +
                content_tag(:span, task[:status], class: 'label label--small label--underline')
            end
        end
    end
  end

  # Render a group header for grouped content
  def render_group_header(title)
    content_tag(:span, title, class: 'label label--medium group-header')
  end

  def framework_nav_groups
    if params[:controller] == 'framework_legacy'
      FrameworkLegacyController::GROUPS
    else
      FrameworkController::GROUPS
    end
  end
end
# rubocop:enable Metrics/ModuleLength
