module CheckoutHelper
  ## BEGIN SOLD OUT / IN STOCK CONFIG UI ##

  # if sold out OR in stock, change this func's return
  def buy_now_cta_text
    t(:buy_now)
    # 'Pre-order'
  end

  # if sold out, change this func's date to reflect incoming inventory
  def sold_out_until
    'Est. Shipping: MON YEAR'
  end

  ## END SOLD OUT / IN STOCK CONFIG UI ##

  def buy_now_hint_text
    in_stock? ? shipping_status : sold_out_status
  end

  def buy_now_hint_text_styles
    in_stock? ? 'hidden' : 'block'
  end

  def sold_out_status
    "Sold Out<br>#{sold_out_until}".html_safe
  end

  # product ships same day if purchased before 2pm
  def shipping_status
    # if hq_time.hour <= 14
    #   "Ships today (#{hq_time.strftime('%b')} #{hq_time.day})"
    # else
    #   tmrw = hq_time.tomorrow
    #   "Ships tomorrow (#{tmrw.strftime('%b')} #{tmrw.day})"
    # end

    'OG Model In Stock'
  end

  def checkout_cta_text
    base = 'Checkout'
    return base if in_stock?

    base + " (#{sold_out_until})"
  end

  def in_stock?
    buy_now_cta_text == t(:buy_now)
  end

  def hq_time
    DateTime.now.in_time_zone('America/New_York')
  end
end
