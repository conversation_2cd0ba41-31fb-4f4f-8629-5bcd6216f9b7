module DevicesHelper
  def current_devices
    current_user&.devices
  end

  def current_device
    current_user.current_device
  end

  def guest_mode_duration_options
    [30, 60, 120, 240, 480].map { |i| ["#{i}m", i.minutes] }
  end

  def wifi_strength_class(strength, index)
    base_classes = "w-1 rounded-sm"
    height_classes = ["h-1", "h-2", "h-3"]

    active = case strength
             when 0..33 then index.zero?
             when 34..66 then index <= 1
             else index <= 2 # 67..100
             end

    bg_color_class = active ? "bg-green-500" : "bg-green-200"
    "#{base_classes} #{height_classes[index]} #{bg_color_class}".strip
  end

  def byod_device_options
    current_user&.admin? ? DeviceModel.all_byod_devices : DeviceModel.visible_byod_devices
  end
end
