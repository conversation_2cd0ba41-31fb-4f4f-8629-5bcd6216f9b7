module PluginSettings<PERSON>elper
  def plugin_preview_image_tag
    keyname = @plugin_setting.recipe? ? @plugin_setting.name.parameterize(separator: '_') : @plugin.keyname
    base_asset_path = @plugin_setting.recipe? ? "plugin_previews/recipes" : "plugin_previews"
    full_asset_path = "#{base_asset_path}/#{keyname}"

    img = if asset_exist?("#{full_asset_path}.bmp")
            "#{full_asset_path}.bmp"
          elsif asset_exist?("#{full_asset_path}.png")
            "#{full_asset_path}.png"
          else
            'plugin_previews/default.bmp'
          end

    img = most_recent_screen.image if screen_available?

    image_tag img, class: "w-full", id: 'current_preview'
  end

  def screen_available?
    @plugin_setting.screen_for(current_device)&.image&.persisted?
  end

  def most_recent_screen
    @plugin_setting.screen_for(current_device)
  end

  def plugin_setting_field_description(field)
    field["description-#{locale}"].present? ? field["description-#{locale}"] : field['description']
  end

  def plugin_setting_field_value(model, field, attr_namespace = nil)
    attr_namespace&.delete!('[]')

    found_value = if field['encrypted']
                    attr_namespace ? model.encrypted_settings&.dig(attr_namespace, field['keyname']) : model.encrypted_settings[field['keyname']]
                  else
                    attr_namespace ? model.settings&.dig(attr_namespace, field['keyname']) : model.settings[field['keyname']]
                  end

    found_value.blank? ? field['default'] : found_value
  end

  # limits markup options for plugins.yml + user-facing form builder 'description', 'help_text' values
  def form_field_sanitizers
    {
      tags: %w[a br strong b],
      attributes: %w[href class target]
    }
  end

  def markup_preview_broadcast_channel(plugin_setting, size)
    "markup-preview-#{plugin_setting.id}-#{size}"
  end

  def download_template(*blob_ids)
    contents = Concurrent::Promises.zip(*blob_ids.map do |blob_id|
      Concurrent::Promises.future(blob_id) do |id|
        id && download_template_content(id)
      end
    end).value!

    Markup::Template.parse(contents.join)
  end

  def download_template_content(blob_id)
    if Rails.env.test? || Rails.env.development?
      return ActiveStorage::Blob.find(blob_id).download.force_encoding('UTF-8')
    end

    file_path = Rails.root.join('storage', blob_id.to_s)

    if File.file?(file_path)
      File.read(file_path)
    else
      content = ActiveStorage::Blob.find(blob_id).download.force_encoding('UTF-8')
      File.write(file_path, content)
      content
    end
  end

  def conditional_validations_data_attributes(field)
    return {} unless field['conditional_validation']

    attrs = {
      conditional_validation_target: "field",
      conditional_validation_rules: {
        field: field['keyname'],
        conditions: field['conditional_validation'].map { |c| { when: c['when'], required: c['required'] } }
      }.to_json,
      action: "change->conditional-validation#validateFields"
    }

    # Add action for conditional visibility if there are hidden fields
    if field['conditional_validation'].any? { |c| c['hidden'].present? }
      attrs[:action] = "change->conditional-validation#validateFields change->conditional-visibility#fieldChanged"
    end

    attrs
  end

  def conditional_visibility_rules(fields)
    rules = []

    fields.each do |field|
      next unless field['conditional_validation']

      conditions_with_hidden = field['conditional_validation'].select { |c| c['hidden'].present? }
      next if conditions_with_hidden.empty?

      rules << {
        field: field['keyname'],
        conditions: conditions_with_hidden.map { |c| { when: c['when'], hidden: c['hidden'] } }
      }
    end

    rules
  end

  def debug_logs_status(plugin_setting)
    return nil unless plugin_setting.debug_logs_enabled?

    hours = (plugin_setting.debug_logs_until - Time.current).to_i / 3600
    t('plugin_settings.form.debug_logs_enabled', hours:)
  end

  def field_preview_data_attributes(field)
    return {} unless field['previewable']

    {
      preview_field: field['keyname']
    }
  end

  def field_is_previewable?(field)
    field['previewable'] == true
  end
end
