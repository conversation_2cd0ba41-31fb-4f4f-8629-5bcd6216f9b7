# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TRMNL is a Ruby on Rails 8.0.2 application that powers e-ink displays with customizable content from various integrations. The platform supports 40+ plugins and allows users to create custom layouts ("mashups") for their devices.

## Essential Commands

### Development
```bash
# Initial setup
bin/setup

# Start development environment in Procfile.dev
bin/dev

# Start Rails server only
bin/rails server

# Start Sidekiq worker
bundle exec sidekiq

# Rails console
bin/rails console
```

### Testing
```bash
# Run all tests
bundle exec rspec

# Run specific test file
bundle exec rspec spec/path/to/test_spec.rb

# Run tests matching pattern
bundle exec rspec spec/services/plugins/google_calendar_plugin_spec.rb:42
```

- Focus on new functionality only
- You only need to cover code paths once, keep it DRY and concise. Keep specs limited to functionality you are working on.
- No loss of coverage for existing functionality
- **Important**: While keeping tests DRY and focused, ensure adequate test coverage including:
  - Happy path scenarios
  - Edge cases (nil values, empty collections, boundary conditions)
  - Error scenarios and exception handling
  - Invalid input handling
- Ensure it is easier to review and understand for future developers while remaining concisely comprehensive
- No loss of coverage for existing functionality
- Ensure it is easier to review and understand for future developers while remaining consicely comprehensive
- Include relevant context and rationale for changes

### Code Quality
```bash
# Run RuboCop (linting)
bundle exec rubocop

# Auto-fix RuboCop issues
bundle exec rubocop -A
```

### Database
```bash
# Run migrations
bin/rails db:migrate

# Reset database
bin/rails db:reset

# Seed database
bin/rails db:seed

# Sync plugins on db/data/plugins.yaml changes
bin/rails plugins:sync
```

### Plugin Development
```bash
# Generate new plugin
rails g plugin PluginName
```

## Architecture Overview

### Core Models
- **Device**: Physical e-ink display (belongs to a user)
- **Plugin**: Integration module (e.g., GoogleCalendarPlugin, RssPlugin)
- **PluginSetting**: User's plugin configuration

### Plugin Architecture
Native plugins are located in `services/plugins/` and follow this pattern:
1. Each plugin inherits from `Plugin` base class
2. Must implement `locals` method to render content
3. Optional methods: `form_fields`, `oauth_options`, `validate`
4. Views in `app/views/plugins/<plugin_name>/`
5. Tests in `spec/services/plugins/<plugin_name>_plugin_spec.rb`

### Testing Strategy
- RSpec for unit and integration tests
- FactoryBot for test data
- SimpleCov for code coverage

### Frontend Assets
- Tailwind CSS for styling
- Stimulus.js for JavaScript behavior
- Images stored in S3 in production

### Deployment
- Uses Hatchbox for deployment
- PostgreSQL for data persistence
- Redis for caching and Sidekiq
- S3 for image storage
- SSL certificates via Let's Encrypt

## Development Workflow

### Adding a New Plugin
1. Generate plugin: `rails g plugin PluginName`
2. Implement required methods in `services/plugins/plugin_name_plugin.rb`
3. Create view template in `app/views/plugins/plugin_name/`
4. Add tests in `spec/services/plugins/plugin_name_plugin_spec.rb`
5. Register plugin in database seeds if needed

### Working with Devices
- Devices check in via `/api/v1/display` endpoint
- Screens are pre-rendered and served as images

### OAuth Integration
- OAuth2 gems handle authentication flow
- Tokens stored encrypted in plugin_settings

## Important Notes

- The database schema is defined in `db/schema.rb`
- Images are processed to grayscale for e-ink
- The plugin system is designed to be extensible