# frozen_string_literal: true

module OauthService
  class <%= name.camelize %> < Base
    protected

    def oauth_site
      # TODO: Set OAuth provider site URL (e.g., 'https://api.example.com')
      raise NotImplementedError, 'Set oauth_site in <%= name.camelize %> service'
    end

    def oauth_authorize_url
      # TODO: Set authorization endpoint (e.g., '/oauth/authorize')
      raise NotImplementedError, 'Set oauth_authorize_url in <%= name.camelize %> service'
    end

    def oauth_token_url
      # TODO: Set token endpoint (e.g., '/oauth/token')
      raise NotImplementedError, 'Set oauth_token_url in <%= name.camelize %> service'
    end

    # Override if needed - additional authorization parameters
    # def authorization_params
    #   { scope: 'read write' }
    # end

    # Override if needed - additional token request parameters
    # def token_params
    #   { headers: { 'Content-Type' => 'application/json' } }
    # end

    # Override if needed - additional refresh token request parameters
    # def refresh_token_params
    #   { headers: { 'Content-Type' => 'application/json' } }
    # end

    # Override if provider returns additional data
    # def extract_token_data(token)
    #   super.merge(
    #     'additional_field' => token.params['additional_field']
    #   ).compact
    # end
  end
end