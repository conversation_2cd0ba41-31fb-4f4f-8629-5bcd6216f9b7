# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OauthService::<%= name.camelize %> do
  let(:service) { described_class.new('<%= keyname %>') }

  before do
    allow(Rails.application.credentials).to receive(:plugins).and_return({
      <%= keyname %>: { client_id: '<%= keyname %>_client_id', client_secret: '<%= keyname %>_client_secret' }
    })
  end

  describe '<%= name.camelize %>-specific configuration' do
    # TODO: Update these with your service's actual OAuth endpoints
    it 'sets correct API endpoints' do
      expect(service.send(:oauth_site)).to eq('https://api.<%= keyname %>.com')
      expect(service.send(:oauth_authorize_url)).to eq('/oauth/authorize')
      expect(service.send(:oauth_token_url)).to eq('/oauth/token')
    end

    # TODO: Add if your service requires specific authorization or token parameters
    # it 'includes <%= name.camelize %>-specific authorization parameters' do
    #   expect(service.send(:authorization_params)).to eq({ scope: 'read write' })
    # end
    #
    # it 'includes <%= name.camelize %>-specific token parameters' do
    #   expect(service.send(:token_params)).to eq({ grant_type: 'authorization_code' })
    # end
  end

  describe '#extract_token_data' do
    let(:base_token_attrs) { { token: 'access_token_123', refresh_token: 'refresh_token_123', expires_at: 1234567890 } }

    it 'extracts standard OAuth token data' do
      mock_token = instance_double(OAuth2::AccessToken, **base_token_attrs, params: {})

      result = service.send(:extract_token_data, mock_token)

      expect(result).to eq({
        'access_token' => 'access_token_123',
        'refresh_token' => 'refresh_token_123',
        'expires_at' => 1234567890
      })
    end

    # TODO: Add tests for service-specific data extraction if applicable
    # it 'extracts <%= name.camelize %>-specific data' do
    #   mock_token = instance_double(OAuth2::AccessToken, **base_token_attrs, params: {
    #     'user_id' => 'user_123',
    #     'team_name' => 'My Team'
    #   })
    #
    #   result = service.send(:extract_token_data, mock_token)
    #
    #   expect(result).to include('user_id' => 'user_123', 'team_name' => 'My Team')
    # end

    it 'filters out nil values' do
      mock_token = instance_double(OAuth2::AccessToken, token: 'access_token_123', refresh_token: nil, expires_at: nil, params: {})

      result = service.send(:extract_token_data, mock_token)

      expect(result).to eq('access_token' => 'access_token_123')
    end
  end

  describe '#refresh_access_token' do
    let(:user) { create(:user, credentials: { 'existing' => 'data' }) }
    let(:refresh_token) { 'refresh_token_123' }
    let(:mock_new_token) do
      instance_double(OAuth2::AccessToken,
                      token: 'new_access_token_456',
                      refresh_token: 'new_refresh_token_456',
                      expires_at: 9876543210)
    end
    let(:mock_client) { instance_double(OAuth2::Client) }

    before do
      allow(OAuth2::Client).to receive(:new).and_return(mock_client)
      allow(mock_client).to receive(:get_token).and_return(mock_new_token)
    end

    # TODO: Uncomment when OAuth methods are implemented
    # it 'refreshes the access token and stores new credentials' do
    #   result = service.refresh_access_token(refresh_token, user)
    #
    #   expect(mock_client).to have_received(:get_token).with(
    #     refresh_token,
    #     grant_type: 'refresh_token'
    #   )
    #
    #   expected_tokens = {
    #     'access_token' => 'new_access_token_456',
    #     'refresh_token' => 'new_refresh_token_456',
    #     'expires_at' => 9876543210
    #   }
    #
    #   expect(result).to eq(expected_tokens)
    #   expect(user.reload.credentials).to eq(
    #     'existing' => 'data',
    #     '<%= keyname %>' => expected_tokens
    #   )
    # end
    #
    # TODO: Add if your service requires specific refresh token parameters
    # it 'passes refresh_token_params to the OAuth client' do
    #   allow(service).to receive(:refresh_token_params).and_return({ client_id: 'test_id' })
    #
    #   service.refresh_access_token(refresh_token, user)
    #
    #   expect(mock_client).to have_received(:get_token).with(
    #     refresh_token,
    #     grant_type: 'refresh_token',
    #     client_id: 'test_id'
    #   )
    # end
  end

  # TODO: Add if your service has an API version constant
  # describe 'API version' do
  #   it 'uses the correct API version' do
  #     expect(described_class::API_VERSION).to eq('v1')
  #   end
  # end
end