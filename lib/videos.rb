class Videos
  # rubocop:disable Metrics/MethodLength
  def self.call
    [
      # {
      #   title: '',
      #   thumbnail: '', # 640x360
      #   url: '',
      #   author: '',
      #   author_thumbnail: 'X-profile.jpg'
      # },
      {
        title: 'Introducing Terminus - RailsConf 2025',
        thumbnail: 'introducing-temrinus-railsconf-2025.jpeg',
        url: 'https://www.youtube.com/watch?v=tqmxnR99oAI',
        author: '<PERSON>',
        author_thumbnail: 'brooke-kuhlmann-profile.jpeg'
      },
      {
        title: '7 tools that quietly improved my life',
        thumbnail: '7-tools-that-quietly-improved-my-life.jpg',
        url: 'https://www.youtube.com/watch?v=TNZElXJ0BdA',
        author: '<PERSON><PERSON>',
        author_thumbnail: 'reysu-profile.jpg'
      },
      {
        title: 'The Fantastic TRMNL eInk Information Display Device',
        thumbnail: 'the-fantastic-TRMNL-eink-information-display-device.jpeg',
        url: 'https://www.youtube.com/watch?v=lyTWUPWInuc',
        author: '<PERSON>',
        author_thumbnail: 'dave-taylor-profile.jpg'
      },
      {
        title: 'Build Your Dream Smart Home Dashboard with Seeed Displays',
        thumbnail: 'seeed-display-TRMNL-dashboard.jpeg',
        url: 'https://www.youtube.com/live/IuwCVK27B5o?t=2192s',
        author: 'Seeed Studio',
        author_thumbnail: 'seeed-studio-profile.jpg'
      },
      {
        title: 'How To Make Plugins for TRMNL',
        thumbnail: 'how-to-build-plugins-saious-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=NijwT-hMRbw',
        author: 'Saious Codes',
        author_thumbnail: 'saious-codes-profile.jpg'
      },
      {
        title: 'TRMNL Update (April 2025) I Really Like It!',
        thumbnail: 'organizing-for-change-update-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=NluG0YS-5S0',
        author: 'Organizing for Change - Ed Finn',
        author_thumbnail: 'organizing-for-change-profile.jpeg'
      },
      {
        title: 'Comfort Zone, Episode 42: Should We Do a Conclave?',
        thumbnail: 'comfort-zone-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=JEE9q4L8-3k&t=1790s',
        author: 'MacStories',
        author_thumbnail: 'macstories-profile.jpg'
      },
      {
        title: 'Upgrade 552: Table for Six',
        thumbnail: 'upgrade-thumbnail.jpeg',
        url: 'https://youtu.be/ESXZ3YC0G5M?si=vc9MsasL3LYXxSHB&t=6167',
        author: 'Casey Liss & Jason Snell',
        author_thumbnail: 'upgrade-profile.jpg'
      },
      {
        title: 'The Next iPhone and Pixel BOTH Leaked!',
        thumbnail: 'wvfrm-podcast-thumbnail.jpeg',
        url: 'https://youtu.be/-1NZc5Xy_SQ?si=TfllS0nyehXVm8V_&t=5275',
        author: 'WVFRM Podcast',
        author_thumbnail: 'wvfrm-podcast-profile.jpeg'
      },
      {
        title: 'TRMNL - Unboxing & First Impressions',
        thumbnail: 'organizing-for-change-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=LpoFI3pKYuI',
        author: 'Organizing for Change - Ed Finn',
        author_thumbnail: 'organizing-for-change-profile.jpeg'
      },
      {
        title: 'TRMNL OSS Server - Alpha Release Demo',
        thumbnail: 'trmnl-byos-thumbnail.jpg',
        url: 'https://youtu.be/3xehPW-PCOM?si=Oo_n-tth8MSv5nq2',
        author: 'TRMNL',
        author_thumbnail: 'trmnl-profile.jpg'
      },
      {
        title: 'This Open-Source E-Ink Screen Is My Favorite New Gadget',
        thumbnail: 'snazzy-labs-thumbnail.jpg',
        url: 'https://youtu.be/eIcZZX10pa4?si=NpQRM3Jb7dU9hbJm',
        author: 'Snazzy Labs',
        author_thumbnail: 'snazzy-labs-profile.jpg'
      },
      {
        title: 'TWiT - This Week in Tech',
        thumbnail: 'twit-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=m5GOa78xTzw&t=7662s',
        author: 'Jason Snell',
        author_thumbnail: 'twit-profile.jpeg'
      },
      {
        title: 'TRMNL - An E-Ink Feed on your Wall - Easy to Set Up and Still Open Source!',
        thumbnail: 'kit-betts-masters-trmnl-review-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=gqbytd9T2ik',
        author: 'Kit Betts-Masters',
        author_thumbnail: 'kit-betts-masters-profile'
      },
      {
        title: 'Building a Tempest Weather Station display',
        thumbnail: 'tempest-dashboard-thumbnail.jpg',
        url: 'https://youtu.be/dgm5_dZrWv8?si=hx9cewnNgxh5tda_',
        author: 'TRMNL',
        author_thumbnail: 'trmnl-profile.jpg'
      },
      {
        title: 'TRMNL + Terminalwire CLI',
        thumbnail: 'terminalwire-thumbnail.jpeg',
        url: 'https://www.youtube.com/watch?v=O9erGsUOm1U',
        author: 'Terminalwire.com',
        author_thumbnail: 'terminalwire-profile.jpg'
      },
      {
        title: "TRMNL- the E Ink Desk Accessory you didn't know you needed",
        thumbnail: 'doc-williams-thumbnail.jpg',
        url: 'https://youtu.be/_pbNSPRAvPM?si=qtb5EIEPhmvx_GOq',
        author: 'Doc Williams',
        author_thumbnail: 'doc-williams-profile.jpg'
      },
      {
        title: 'Introducing TRMNL - the e-ink solution to staying focused',
        thumbnail: 'ryan-kulp-thumbnail.jpg',
        url: 'https://youtu.be/WnAFZDzgWy4?si=7fyD_qmtGLKoJoc7',
        author: 'Ryan Kulp',
        author_thumbnail: 'ryan-kulp-profile.jpg'
      },
      {
        title: 'Building a custom e-ink dashboard plugin with TRMNL',
        thumbnail: 'custom-plugin-thumbnail.jpg',
        url: 'https://youtu.be/Ofb-mp_x_gM?si=Mptu2nCzi9dQpp1z',
        author: 'TRMNL',
        author_thumbnail: 'trmnl-profile.jpg'
      }
    ]
  end
  # rubocop:enable Metrics/MethodLength
end
