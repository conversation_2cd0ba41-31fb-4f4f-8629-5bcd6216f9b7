# frozen_string_literal: true

require 'rails_helper'

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('openapi').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under openapi_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding a openapi_spec tag to the
  # the root example_group in your specs, e.g. describe '...', openapi_spec: 'v2/swagger.json'
  config.openapi_specs = {
    'openapi.yaml' => {
      openapi: '3.0.1',
      info: {
        title: 'TRMNL API',
        version: '1'
      },
      paths: {},
      servers: [
        {
          url: 'https://{defaultHost}/api',
          variables: {
            defaultHost: {
              default: 'usetrmnl.com'
            }
          }
        }
      ],
      components: {
        securitySchemes: {
          bearer_auth: {
            type: :http,
            scheme: :bearer,
            bearerFormat: 'Account API key'
          }
        },
        schemas: {
          Error: {
            type: :object,
            additionalProperties: false,
            properties: {
              error: { type: :string, example: 'An error occurred' }
            }
          },
          Device: {
            type: :object,
            additionalProperties: false,
            properties: {
              id: { type: :integer, example: 123 },
              name: { type: :string, example: 'My TRMNL' },
              friendly_id: { type: :string, example: 'ABC-123' },
              mac_address: { type: :string, example: '12:34:56:78:9A:BC' },
              battery_voltage: { type: :number, nullable: true, example: 3.7 },
              rssi: { type: :integer, nullable: true, example: -70 },
              percent_charged: { type: :number, example: 85.0, minimum: 0, maximum: 100 },
              wifi_strength: { type: :number, example: 75.0, minimum: 0, maximum: 100 }
            }
          },
          Model: {
            type: :object,
            additionalProperties: false,
            properties: {
              name: { type: :string, example: 'trmnl_original', description: 'Unique identifier' },
              label: { type: :string, example: 'TRMNL', description: 'Human-readable name' },
              description: { type: :string, example: 'Original TRMNL model', description: 'Description' },
              width: { type: :integer, example: 800, description: 'Screen width in pixels' },
              height: { type: :integer, example: 480, description: 'Screen height in pixels' },
              colors: { type: :integer, example: 2, description: 'Number of colors supported' },
              bit_depth: { type: :integer, example: 1, description: 'Color bit depth' },
              scale_factor: { type: :number, example: 1.0, description: 'Display scale factor' },
              rotation: { type: :integer, example: 90, description: 'Screen rotation in degrees' },
              mime_type: { type: :string, example: 'image/png', description: 'Image MIME type' },
              offset_x: { type: :integer, example: 10, description: 'X offset for image rendering' },
              offset_y: { type: :integer, example: 20, description: 'Y offset for image rendering' },
              published_at: { type: :string, format: :date_time, example: '2023-10-01T12:00:00Z', description: 'Publication date in RFC 3339 format' }
            }
          },
          PlaylistItem: {
            type: :object,
            additionalProperties: false,
            properties: {
              created_at: { type: :string, format: :date_time, example: '2023-10-01T12:00:00Z' },
              device_id: { type: :integer, example: 1 },
              id: { type: :integer, example: 1 },
              mashup_id: { type: :integer, example: 1, nullable: true },
              mirror: { type: :boolean, example: true },
              playlist_group_id: { type: :integer, example: 1 },
              plugin_setting: { '$ref' => '#/components/schemas/PluginSetting' },
              plugin_setting_id: { type: :integer, example: 1 },
              rendered_at: { type: :string, format: :date_time, example: '2023-10-01T12:00:00Z' },
              row_order: { type: :integer, example: 1 },
              updated_at: { type: :string, format: :date_time, example: '2023-10-01T12:00:00Z' },
              visible: { type: :boolean, example: true }
            }
          },
          PlaylistItemParams: {
            type: :object,
            additionalProperties: false,
            properties: {
              visible: { type: :boolean, example: true }
            }
          },

          PluginSetting: {
            type: :object,
            additionalProperties: false,
            properties: {
              id: { type: :integer, example: 1 },
              name: { type: :string, example: 'My Plugin Setting' },
              plugin_id: { type: :integer, example: 1 }
            }
          },
          PluginSettingArchive: {
            type: :object,
            properties: {
              settings_yaml: {
                type: :string,
                description: 'YAML settings file'
              }
            }
          },
          PluginSettingParams: {
            type: :object,
            additionalProperties: false,
            properties: {
              name: { type: :string, example: 'My Plugin Setting', required: true },
              plugin_id: { type: :integer, example: 1, required: true }
            }
          }
        }
      }
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The openapi_specs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end
