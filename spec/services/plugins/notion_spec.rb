require 'rails_helper'

describe Plugins::Notion do
  let(:user) { create(:user) }
  let(:plugin) { Plugin.find_by_keyname('notion') || create(:plugin, keyname: 'notion', name: 'Notion', form_type: 'oauth2') }
  let(:api_client) { instance_double(Plugins::Notion::ApiClient) }
  let(:access_token) { 'test-access-token' }
  let(:test_database_id) { 'test-db-id' }

  describe 'class methods' do
    let(:credentials) { { 'notion' => { 'access_token' => access_token } } }

    describe '.list_databases' do
      it 'calls ApiClient.list_databases with access token' do
        expect(Plugins::Notion::ApiClient).to receive(:new)
          .with(access_token)
          .and_return(api_client)
        expect(api_client).to receive(:list_databases).and_return([])

        result = described_class.list_databases(credentials)
        expect(result).to eq([])
      end

      it 'returns empty array when access token is missing' do
        result = described_class.list_databases({})
        expect(result).to eq([])
      end

      it 'handles API errors gracefully' do
        allow(Plugins::Notion::ApiClient).to receive(:new).and_raise(StandardError.new('API Error'))
        expect(Rails.logger).to receive(:error).with('Notion API error: API Error')

        result = described_class.list_databases(credentials)
        expect(result).to eq([])
      end
    end

    describe '.search_databases' do
      it 'calls ApiClient.search_databases with query' do
        expect(Plugins::Notion::ApiClient).to receive(:new)
          .with(access_token)
          .and_return(api_client)
        expect(api_client).to receive(:search_databases).with('test query').and_return([])

        result = described_class.search_databases(credentials, 'test query')
        expect(result).to eq([])
      end
    end
  end

  describe '#locals' do
    let(:service) do
      described_class.new(plugin_setting)
    end

    before do
      allow(service).to receive(:api_client).and_return(api_client)
    end

    let(:plugin_setting) do
      create(:plugin_setting,
             plugin_id: plugin.id,
             user: user,
             settings: settings,
             encrypted_settings: { 'notion' => { 'access_token' => access_token } })
    end

    context 'when missing required configuration' do
      let(:settings) { { 'notion' => { 'access_token' => '' } } }

      it 'returns error for missing access token' do
        result = service.locals
        expect(result).to eq({ error: "Missing integration token or resource ID" })
      end
    end

    context 'when configured for database display' do
      let(:settings) do
        {
          'notion' => { 'access_token' => access_token },
          'display_type' => 'database',
          'notion_database_id' => test_database_id,
          'multi_column_display' => '2',
          'image_height' => '100',
          'status_field' => 'Status',
          'labeled_properties' => 'Tag,Priority',
          'listed_properties' => 'Owner,Due Date'
        }
      end

      let(:database_response) do
        {
          'results' => [
            {
              'properties' => {
                'Name' => {
                  'type' => 'title',
                  'title' => [{ 'plain_text' => 'Test Task 📝' }]
                },
                'Status' => {
                  'type' => 'select',
                  'select' => { 'name' => 'In Progress' }
                }
              },
              'url' => 'https://notion.so/test',
              'last_edited_time' => '2025-01-15T10:00:00.000Z'
            }
          ]
        }
      end

      before do
        allow(api_client).to receive(:query_database).and_return(database_response)
      end

      it 'returns database items with emoji stripping' do
        result = service.locals

        expect(result[:display_type]).to eq('database')
        expect(result[:multi_column_display]).to eq('2')
        expect(result[:image_height]).to eq(100)
        expect(result[:status_field]).to eq('Status')
        expect(result[:labeled_properties]).to eq(['Tag', 'Priority'])
        expect(result[:listed_properties]).to eq(['Owner', 'Due Date'])

        expect(result[:items]).to be_an(Array)
        expect(result[:items].first[:title]).to eq('Test Task') # emoji stripped
        expect(result[:items].first[:url]).to eq('https://notion.so/test')
        expect(result[:items].first[:last_edited]).to eq('Jan 15, 2025')
      end
    end

    context 'when configured for page display' do
      let(:settings) do
        {
          'notion' => { 'access_token' => access_token },
          'display_type' => 'page',
          'notion_page_id' => 'test-page-id',
          'multi_column_display' => '1',
          'image_height' => '150'
        }
      end

      let(:page_response) do
        {
          'properties' => {
            'title' => {
              'type' => 'title',
              'title' => [{ 'plain_text' => 'Test Page' }]
            }
          },
          'url' => 'https://notion.so/page',
          'last_edited_time' => '2025-01-15T10:00:00.000Z'
        }
      end

      let(:blocks_response) do
        {
          'results' => [
            {
              'type' => 'paragraph',
              'paragraph' => {
                'rich_text' => [{ 'plain_text' => 'Test content with 🚀 emoji' }]
              }
            }
          ]
        }
      end

      before do
        allow(api_client).to receive(:get_page_info).and_return(page_response)
        allow(api_client).to receive(:get_page_blocks).and_return(blocks_response)
      end

      it 'returns page content with emoji stripping' do
        result = service.locals

        expect(result[:display_type]).to eq('page')
        expect(result[:items][:blocks]).to be_an(Array)
        expect(result[:items][:blocks].first[:text]).to eq('Test content with  emoji') # emoji stripped but double space remains
        expect(result[:items][:url]).to eq('https://notion.so/page')
      end
    end

    context 'with invalid display type' do
      let(:settings) do
        {
          'notion' => { 'access_token' => access_token },
          'display_type' => 'invalid',
          'notion_database_id' => 'test-id'
        }
      end

      it 'returns error for invalid display type' do
        result = service.locals
        expect(result).to eq({ error: "Invalid display type" })
      end
    end
  end

  describe 'private methods' do
    let(:service) do
      described_class.new(plugin_setting)
    end

    let(:plugin_setting) do
      create(:plugin_setting,
             plugin_id: plugin.id,
             user: user,
             settings: {
               'notion_database_id' => 'test-db-id'
             },
             encrypted_settings: { 'notion' => { 'access_token' => access_token } })
    end

    describe '#strip_emojis' do
      it 'removes emojis from text' do
        result = service.send(:strip_emojis, 'Test 📝 text 🚀')
        expect(result).to eq('Test  text') # emoji stripped
      end

      it 'handles non-string input' do
        result = service.send(:strip_emojis, nil)
        expect(result).to be_nil
      end
    end

    describe '#format_property_value' do
      it 'formats title property' do
        property = {
          'type' => 'title',
          'title' => [{ 'plain_text' => 'Test Title 📝' }]
        }
        result = service.send(:format_property_value, property)
        expect(result).to eq('Test Title') # emoji stripped
      end

      it 'formats select property' do
        property = {
          'type' => 'select',
          'select' => { 'name' => 'In Progress 🔄' }
        }
        result = service.send(:format_property_value, property)
        expect(result).to eq('In Progress') # emoji stripped
      end

      it 'formats number property without stripping' do
        property = {
          'type' => 'number',
          'number' => 42
        }
        result = service.send(:format_property_value, property)
        expect(result).to eq(42)
      end

      it 'formats date property' do
        property = {
          'type' => 'date',
          'date' => { 'start' => '2025-01-15' }
        }
        result = service.send(:format_property_value, property)
        expect(result).to eq('Jan 15, 2025')
      end

      it 'formats checkbox property' do
        property = {
          'type' => 'checkbox',
          'checkbox' => true
        }
        result = service.send(:format_property_value, property)
        expect(result).to eq('Yes')
      end
    end

    describe '#format_date' do
      it 'formats valid date strings' do
        result = service.send(:format_date, '2025-01-15T10:00:00.000Z')
        expect(result).to eq('Jan 15, 2025')
      end

      it 'handles invalid date strings' do
        result = service.send(:format_date, 'invalid-date')
        expect(result).to eq('invalid-date')
      end

      it 'handles nil input' do
        result = service.send(:format_date, nil)
        expect(result).to eq('')
      end
    end
  end

  describe '#notion_database_id' do
    let(:service) do
      described_class.new(plugin_setting)
    end

    let(:plugin_setting) do
      create(:plugin_setting,
             plugin_id: plugin.id,
             user: user,
             settings: settings,
             encrypted_settings: { 'notion' => { 'access_token' => access_token } })
    end

    context 'when setting contains id::name format' do
      let(:settings) { { 'notion_database_id' => 'db-123-456::Project Tasks Database' } }

      it 'extracts and returns only the ID part' do
        expect(service.notion_database_id).to eq('db-123-456')
      end
    end

    context 'when setting contains only ID' do
      let(:settings) { { 'notion_database_id' => 'db-simple-id' } }

      it 'returns the ID as is' do
        expect(service.notion_database_id).to eq('db-simple-id')
      end
    end

    context 'when setting is blank' do
      let(:settings) { { 'notion_database_id' => '' } }

      it 'returns nil' do
        expect(service.notion_database_id).to be_nil
      end
    end

    context 'when setting is nil' do
      let(:settings) { {} }

      it 'returns nil' do
        expect(service.notion_database_id).to be_nil
      end
    end

    context 'when setting contains multiple :: separators' do
      let(:settings) { { 'notion_database_id' => 'db-456::Name::With::Colons' } }

      it 'returns only the first part before first ::' do
        expect(service.notion_database_id).to eq('db-456')
      end
    end
  end

  describe '#notion_page_id' do
    let(:service) do
      described_class.new(plugin_setting)
    end

    let(:plugin_setting) do
      create(:plugin_setting,
             plugin_id: plugin.id,
             user: user,
             settings: settings,
             encrypted_settings: { 'notion' => { 'access_token' => access_token } })
    end

    context 'when setting contains id::name format' do
      let(:settings) { { 'notion_page_id' => 'page-789-abc::Company Wiki Page' } }

      it 'extracts and returns only the ID part' do
        expect(service.notion_page_id).to eq('page-789-abc')
      end
    end

    context 'when setting contains only ID' do
      let(:settings) { { 'notion_page_id' => 'page-simple-id' } }

      it 'returns the ID as is' do
        expect(service.notion_page_id).to eq('page-simple-id')
      end
    end

    context 'when setting is blank' do
      let(:settings) { { 'notion_page_id' => '' } }

      it 'returns nil' do
        expect(service.notion_page_id).to be_nil
      end
    end

    context 'when setting is nil' do
      let(:settings) { {} }

      it 'returns nil' do
        expect(service.notion_page_id).to be_nil
      end
    end

    context 'when setting contains multiple :: separators' do
      let(:settings) { { 'notion_page_id' => 'page-def::Meeting::Notes::2025' } }

      it 'returns only the first part before first ::' do
        expect(service.notion_page_id).to eq('page-def')
      end
    end
  end
end
