require 'rails_helper'
require 'sidekiq/testing'

describe Plugins::Base do
  let(:user) { create(:user) }
  let!(:device) { create(:device, user_id: user.id) }
  let(:service) do
    plugin_setting.service
  end

  let(:verse_response) do
    { "reference" => "Ezra 6:6",
      "verses" =>
        [{ "book_id" => "EZR",
           "book_name" => "Ezra",
           "chapter" => 6,
           "verse" => 6,
           "text" =>
             "Now therefore, <PERSON><PERSON><PERSON>, governor beyond the River, Shethar-bozenai, and your companions the Apharsachites, who are beyond the River, be ye far from thence:" }],
      "text" => "Now therefore, <PERSON><PERSON><PERSON>, governor beyond the River, Shethar-bozenai, and your companions the Apharsachites, who are beyond the River, be ye far from thence:",
      "translation_id" => "asv",
      "translation_name" => "American Standard Version (1901)",
      "translation_note" => "Public Domain" }
  end

  let(:formatted_verse) do
    {
      verse:
        {
          reference: "Ezra 6:6",
          text: "Now therefore, <PERSON><PERSON><PERSON>, governor beyond the River, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, and your companions the Apharsachites, who are beyond the River, be ye far from thence:"
        },
      bible_name: "american_standard"
    }
  end

  context 'native plugins' do
    let(:plugin) { Plugin.find_by_keyname('bible_verses') }
    let(:plugin_setting) { create(:plugin_setting, plugin_id: plugin.id, user: user, settings: { bible: 'american_standard' }) }

    describe '#process!' do
      before do
        ActiveStorage::Current.url_options = { host: Rails.application.credentials.base_url }
        stub_request(:get, 'https://bible-api.com/?random=verse&translation=asv')
          .with(
            headers: {
              'Accept' => '*/*',
              'Accept-Encoding' => 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
              'User-Agent' => 'Ruby'
            }
          )
          .to_return(status: 200, body: verse_response.to_json, headers: { "Content-Type" => 'application/json' })
      end

      context 'plugin_setting instance' do
        before { create(:playlist_item, device: device, plugin_setting: plugin_setting) }

        it 'should process the image and save it on disk' do
          expect(service.process!.first).to eq(true)
        end
      end

      context 'mashup? (is true)' do
        before do
          Sidekiq::Testing.fake!
          mashup = create(:mashup, user: user, layout: '1x1')
          mashup.contents_required.times do
            create(:mashup_content, mashup:, position: 'a', plugin_setting_id: plugin_setting.id)
          end
          plugin_setting.reload
        end

        context 'preview image exists' do
          it 'should skip processing image' do
            expect(plugin_setting.screens.count).to eq(1)
            expect(plugin_setting.persistence_data).to match({})
            expect(service.process!).to match(nil)
            plugin_setting.reload
            expect(plugin_setting.persistence_data.deep_symbolize_keys).to eq({ locals: formatted_verse })
          end
        end

        context 'force_refresh=true' do
          before do
            service.force_refresh = true
          end

          it 'should skip processing image and trigger force_refresh!' do
            Sidekiq::Worker.clear_all
            expect(plugin_setting.persistence_data).to match({})
            expect { service.process! }.to change { Plugins::ForceRefreshWorker.jobs.size }.by 1
            plugin_setting.reload
            expect(plugin_setting.persistence_data.deep_symbolize_keys).to eq({ locals: formatted_verse })
          end
        end

        context 'refreshes 1x/day' do
          before do
            plugin_setting.update(refresh_interval: 1440) # setting plugin_setting to refresh just 1x/day.
            plugin_setting.reload
          end

          it 'should process mashups immediately.' do
            Sidekiq::Worker.clear_all
            expect(plugin_setting.persistence_data).to match({})
            expect { service.process! }.to change { Plugins::ForceRefreshWorker.jobs.size }.by 1
            plugin_setting.reload
            expect(plugin_setting.persistence_data.deep_symbolize_keys).to eq({ locals: formatted_verse })
          end
        end

        context 'preview image does not exists' do
          before do
            plugin_setting.screens.destroy_all
            plugin_setting.reload
          end

          it 'should process the image and save it on disk' do
            expect(plugin_setting.persistence_data).to match({})
            expect(service.process!).to match([true])
            plugin_setting.reload
            expect(plugin_setting.persistence_data.deep_symbolize_keys).to eq({ locals: {
              bible_name: "american_standard",
              verse: {
                reference: "Ezra 6:6",
                text: "Now therefore, Tattenai, governor beyond the River, Shethar-bozenai, and your companions the Apharsachites, who are beyond the River, be ye far from thence:"
              }
            } })
          end
        end
      end
    end
  end

  context 'private_plugins' do
    let(:plugin) { Plugin.find_by_keyname('private_plugin') }
    let(:plugin_setting) do
      create(:plugin_setting, plugin_id: plugin.id, user: user,
                              settings: { strategy: 'polling', polling_url: 'https://bible-api.com/?random=verse&translation=asv' },
                              model_ids: [1])
    end

    describe '#process!' do
      before do
        ActiveStorage::Current.url_options = { host: Rails.application.credentials.base_url }
        stub_request(:get, 'https://bible-api.com/?random=verse&translation=asv')
          .with(
            headers: {
              'Accept' => '*/*',
              'Accept-Encoding' => 'gzip;q=1.0,deflate;q=0.6,identity;q=0.3',
              'User-Agent' => 'Ruby'
            }
          )
          .to_return(status: 200, body: verse_response.to_json, headers: { "Content-Type" => 'application/json' })
        plugin_setting.attach_markup(:full, '<html> </html>')
      end

      it "does not generate screen if only global variables change" do
        Timecop.travel(Date.parse('2025-04-23').in_time_zone('Sydney').change({ hour: 12, min: 0 }))
        expect(plugin_setting.service.process!.first).to eq(true)
        Timecop.travel(Date.parse('2025-04-23').in_time_zone('Sydney').change({ hour: 12, min: 15 })) # data remains same, just time gets changed.
        expect(plugin_setting.service.process!.first).to eq(nil)
      end

      describe 'debug logs' do
        context 'with debug mode enabled' do
          before { plugin_setting.enable_debug_logs! }

          it 'should be created' do
            expect(plugin_setting.logs.info.count).to eq(1)
            expect(plugin_setting.logs.info.first.dump).to include('Debug logs enabled')
            expect(plugin_setting.logs.debug.count).to eq(0)

            plugin_setting.service.process!

            plugin_setting.reload
            expect(plugin_setting.logs.debug.count).to be > 0
          end
        end

        context 'with debug mode disabled' do
          it 'should not be created' do
            expect(plugin_setting.logs.info.count).to eq(0)
            expect(plugin_setting.logs.debug.count).to eq(0)

            plugin_setting.service.process!

            plugin_setting.reload
            expect(plugin_setting.logs.debug.count).to eq(0)
          end
        end
      end
    end

    describe 'error handling in development environment' do
      let(:plugin) { Plugin.find_by_keyname('bible_verses') }
      let(:plugin_setting) { create(:plugin_setting, plugin_id: plugin.id, user: user, settings: { bible: 'american_standard' }) }

      before do
        create(:playlist_item, device: device, plugin_setting: plugin_setting)
        ActiveStorage::Current.url_options = { host: Rails.application.credentials.base_url }
      end

      shared_examples 'handles errors based on environment' do |error_class, error_message|
        before do
          allow(service).to receive(:locals).and_raise(error_class.new(error_message))
        end

        it 're-raises error in development environment' do
          allow(Rails.env).to receive(:development?).and_return(true)
          expect(Rails.logger).to receive(:info).with(/#{error_class.name.split('::').last}/)

          expect { service.process! }.to raise_error(error_class, error_message)
        end

        it 'suppresses error in non-development environments' do
          allow(Rails.env).to receive(:development?).and_return(false)
          expect(Rails.logger).to receive(:info).with(/#{error_class.name.split('::').last}/)

          expect { service.process! }.not_to raise_error
        end
      end

      context 'when DataFetchError is raised' do
        include_examples 'handles errors based on environment', Plugins::Base::DataFetchError, 'API unavailable'
      end

      context 'when StandardError is raised' do
        include_examples 'handles errors based on environment', StandardError, 'Generic error'
      end
    end
  end
end
