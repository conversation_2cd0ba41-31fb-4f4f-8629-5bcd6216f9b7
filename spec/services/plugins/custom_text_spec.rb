require 'rails_helper'

describe Plugins::CustomText do
  let(:service) do
    @plugin_setting.service
  end

  describe '#locals' do
    before do
      plugin = Plugin.find_by_keyname('custom_text')
      @plugin_setting = create(:plugin_setting, plugin_id: plugin.id, settings: { phrases: 'I am gay; You are gay; We are all gay', font_scaling: 'fixed' })
    end

    context 'process!' do
      it 'should assign values to locals hash' do
        expect(service.locals.keys).to eql([:phrase, :font_scaling, :text_alignment, :title_bar_text])
        expect(service.locals[:phrase]).to include('gay')
        expect(service.locals[:phrase]).to_not include(';')
        expect(service.locals[:font_scaling]).to eql('fixed')
        expect(service.locals[:text_alignment]).to eql('center')
        expect(service.locals[:title_bar_text]).to eql('')
        expect(service.locals[:title_bar_text]).to_not be_nil
      end

      it 'should use specified text alignment' do
        @plugin_setting.update(settings: { phrases: 'Test phrase', font_scaling: 'fixed', text_alignment: 'left' })
        expect(@plugin_setting.reload.service.locals[:text_alignment]).to eql('left')

        @plugin_setting.update(settings: { phrases: 'Test phrase', font_scaling: 'fixed', text_alignment: 'right' })
        expect(@plugin_setting.reload.service.locals[:text_alignment]).to eql('right')
      end

      it 'should default to center alignment when text_alignment is not specified' do
        @plugin_setting.update(settings: { phrases: 'Test phrase', font_scaling: 'fixed' })
        expect(@plugin_setting.reload.service.locals[:text_alignment]).to eql('center')
      end
    end
  end
end
