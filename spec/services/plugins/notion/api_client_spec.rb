require 'rails_helper'

describe Plugins::Notion::ApiClient do
  let(:access_token) { 'test-access-token' }
  let(:api_client) { described_class.new(access_token) }

  describe '#initialize' do
    it 'sets the access token' do
      expect(api_client.instance_variable_get(:@access_token)).to eq(access_token)
    end
  end

  describe '#list_databases' do
    let(:search_response) do
      {
        'results' => [
          {
            'id' => 'db-1',
            'title' => [{ 'plain_text' => 'Database 1' }]
          },
          {
            'id' => 'db-2',
            'title' => [{ 'plain_text' => 'Database 2' }]
          }
        ]
      }
    end

    before do
      stub_request(:post, 'https://api.notion.com/v1/search')
        .with(
          body: {
            filter: { property: 'object', value: 'database' },
            page_size: 100
          }.to_json,
          headers: {
            'Authorization' => "Bearer #{access_token}",
            'Notion-Version' => '2022-06-28',
            'Content-Type' => 'application/json'
          }
        )
        .to_return(status: 200, body: search_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'returns formatted database list' do
      result = api_client.list_databases
      expect(result).to eq([
                             { 'Database 1' => 'db-1' },
                             { 'Database 2' => 'db-2' }
                           ])
    end

    it 'handles API errors gracefully' do
      stub_request(:post, 'https://api.notion.com/v1/search')
        .to_raise(StandardError.new('Network error'))

      expect(Rails.logger).to receive(:error)
        .with('Notion API error searching databases: Network error')

      result = api_client.list_databases
      expect(result).to eq([])
    end
  end

  describe '#search_databases' do
    let(:search_response) do
      {
        'results' => [
          {
            'id' => 'db-1',
            'title' => [{ 'plain_text' => 'Task Database' }]
          }
        ]
      }
    end

    before do
      stub_request(:post, 'https://api.notion.com/v1/search')
        .with(
          body: {
            filter: { property: 'object', value: 'database' },
            page_size: 100,
            query: 'task'
          }.to_json
        )
        .to_return(status: 200, body: search_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'searches databases with query' do
      result = api_client.search_databases('task')
      expect(result).to eq([
                             { id: 'db-1', name: 'Task Database', text: 'Task Database' }
                           ])
    end
  end

  describe '#search_pages' do
    let(:search_response) do
      {
        'results' => [
          {
            'id' => 'page-1',
            'properties' => {
              'title' => {
                'type' => 'title',
                'title' => [{ 'plain_text' => 'Meeting Notes' }]
              }
            }
          }
        ]
      }
    end

    before do
      stub_request(:post, 'https://api.notion.com/v1/search')
        .with(
          body: {
            filter: { property: 'object', value: 'page' },
            page_size: 100,
            query: 'meeting'
          }.to_json
        )
        .to_return(status: 200, body: search_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'searches pages with query and increased page size' do
      result = api_client.search_pages('meeting')
      expect(result).to eq([
                             { id: 'page-1', name: 'Meeting Notes', text: 'Meeting Notes' }
                           ])
    end
  end

  describe '#query_database' do
    let(:database_id) { 'test-db-id' }
    let(:query_response) do
      {
        'results' => [
          {
            'properties' => {
              'Name' => {
                'type' => 'title',
                'title' => [{ 'plain_text' => 'Test Item' }]
              }
            }
          }
        ]
      }
    end

    before do
      stub_request(:post, "https://api.notion.com/v1/databases/#{database_id}/query")
        .with(
          body: {
            page_size: 20,
            sorts: []
          }.to_json
        )
        .to_return(status: 200, body: query_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'queries database with default parameters' do
      result = api_client.query_database(database_id)
      expect(result).to eq(query_response)
    end

    it 'handles custom sorts parameter' do
      sorts = [{ 'property' => 'Created', 'direction' => 'ascending' }]

      stub_request(:post, "https://api.notion.com/v1/databases/#{database_id}/query")
        .with(
          body: {
            page_size: 50,
            sorts: sorts
          }.to_json
        )
        .to_return(status: 200, body: query_response.to_json, headers: { 'Content-Type' => 'application/json' })

      result = api_client.query_database(database_id, page_size: 50, sorts: sorts)
      expect(result).to eq(query_response)
    end
  end

  describe '#get_page_info' do
    let(:page_id) { 'test-page-id' }
    let(:page_response) do
      {
        'properties' => {
          'title' => {
            'type' => 'title',
            'title' => [{ 'plain_text' => 'Test Page' }]
          }
        },
        'url' => 'https://notion.so/page'
      }
    end

    before do
      stub_request(:get, "https://api.notion.com/v1/pages/#{page_id}")
        .to_return(status: 200, body: page_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'retrieves page information' do
      result = api_client.get_page_info(page_id)
      expect(result).to eq(page_response)
    end
  end

  describe '#get_page_blocks' do
    let(:page_id) { 'test-page-id' }
    let(:blocks_response) do
      {
        'results' => [
          {
            'type' => 'paragraph',
            'paragraph' => {
              'rich_text' => [{ 'plain_text' => 'Test content' }]
            }
          }
        ]
      }
    end

    before do
      stub_request(:get, "https://api.notion.com/v1/blocks/#{page_id}/children?page_size=20")
        .to_return(status: 200, body: blocks_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it 'retrieves page blocks' do
      result = api_client.get_page_blocks(page_id)
      expect(result).to eq(blocks_response)
    end

    it 'handles custom page size' do
      stub_request(:get, "https://api.notion.com/v1/blocks/#{page_id}/children?page_size=50")
        .to_return(status: 200, body: blocks_response.to_json, headers: { 'Content-Type' => 'application/json' })

      result = api_client.get_page_blocks(page_id, page_size: 50)
      expect(result).to eq(blocks_response)
    end
  end

  describe 'error handling' do
    let(:database_id) { 'test-db-id' }

    context 'when API request fails' do
      before do
        stub_request(:post, "https://api.notion.com/v1/databases/#{database_id}/query")
          .to_return(status: 401, body: { error: 'Unauthorized' }.to_json)
      end

      it 'returns nil for failed requests' do
        result = api_client.query_database(database_id)
        expect(result).to be_nil
      end
    end

    context 'when network error occurs' do
      before do
        stub_request(:post, "https://api.notion.com/v1/databases/#{database_id}/query")
          .to_raise(Timeout::Error.new('Timeout'))
      end

      it 'logs error and returns nil' do
        expect(Rails.logger).to receive(:error)
          .with(/Notion API error querying database.*Timeout/)

        result = api_client.query_database(database_id)
        expect(result).to be_nil
      end
    end
  end

  describe 'title extraction' do
    describe '#extract_title' do
      context 'for database items' do
        let(:database_item) do
          {
            'title' => [{ 'plain_text' => 'Test Database' }]
          }
        end

        it 'extracts title from database items' do
          result = api_client.send(:extract_title, database_item)
          expect(result).to eq('Test Database')
        end

        it 'returns default title for databases without title' do
          result = api_client.send(:extract_title, { 'title' => [] })
          expect(result).to eq('Untitled Database')
        end
      end

      context 'for page items' do
        let(:page_item) do
          {
            'properties' => {
              'Name' => {
                'type' => 'title',
                'title' => [{ 'plain_text' => 'Test Page' }]
              }
            }
          }
        end

        it 'extracts title from page properties' do
          result = api_client.send(:extract_title, page_item)
          expect(result).to eq('Test Page')
        end

        it 'tries different title field names' do
          page_with_title_field = {
            'properties' => {
              'Title' => {
                'type' => 'title',
                'title' => [{ 'plain_text' => 'Page Title' }]
              }
            }
          }

          result = api_client.send(:extract_title, page_with_title_field)
          expect(result).to eq('Page Title')
        end

        it 'returns default title for pages without title' do
          result = api_client.send(:extract_title, { 'properties' => {} })
          expect(result).to eq('Untitled Page')
        end
      end
    end
  end
end
