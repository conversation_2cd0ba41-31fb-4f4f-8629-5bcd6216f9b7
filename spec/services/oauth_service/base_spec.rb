# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OauthService::Base do
  let(:test_service_class) do
    Class.new(described_class) do
      protected

      def oauth_site = 'https://api.test.com'
      def oauth_authorize_url = '/oauth/authorize'
      def oauth_token_url = '/oauth/token'
    end
  end

  let(:service) { test_service_class.new('test_plugin') }
  let(:user) { create(:user, credentials: { 'existing' => 'data' }) }
  let(:mock_token_data) { { 'access_token' => 'access_token_123', 'expires_at' => 1234567890 } }

  before do
    allow(Rails.application.credentials).to receive(:plugins).and_return({
      test_plugin: { client_id: 'test_client_id', client_secret: 'test_client_secret' },
      test: {}
    })
    allow(Rails.application.credentials).to receive(:base_url).and_return('https://example.com')
  end

  describe '#initialize' do
    it 'sets plugin_keyname and credentials' do
      expect(service.plugin_keyname).to eq('test_plugin')
      expect(service.credentials[:client_id]).to eq('test_client_id')
    end
  end

  describe '#store_credentials' do
    let(:tokens) { { 'access_token' => 'new_token' } }

    it 'stores credentials preserving existing ones' do
      service.store_credentials(tokens, user)
      expect(user.reload.credentials).to eq('existing' => 'data', 'test_plugin' => tokens)
    end

    it 'raises error on update failure' do
      allow(user).to receive(:update).and_return(false)
      allow(user).to receive_message_chain(:errors, :full_messages).and_return(['Validation failed'])

      expect { service.store_credentials(tokens, user) }.to raise_error('Failed to store OAuth credentials: Validation failed')
    end
  end

  describe 'abstract methods' do
    let(:base_service) { described_class.new('test') }

    it 'enforces implementation of required methods' do
      %i[oauth_site oauth_authorize_url oauth_token_url].each do |method|
        expect { base_service.send(method) }.to raise_error(NotImplementedError)
      end
    end
  end

  describe '#refresh_access_token' do
    let(:user) { create(:user, credentials: { 'existing' => 'data' }) }
    let(:refresh_token) { 'refresh_token_123' }
    let(:mock_new_token) do
      instance_double(OAuth2::AccessToken,
                      token: 'new_access_token_456',
                      refresh_token: 'new_refresh_token_456',
                      expires_at: 9876543210)
    end
    let(:mock_client) { instance_double(OAuth2::Client) }

    before do
      allow(OAuth2::Client).to receive(:new).and_return(mock_client)
      allow(mock_client).to receive(:get_token).and_return(mock_new_token)
    end

    it 'refreshes the access token and stores new credentials' do
      result = service.refresh_access_token(refresh_token, user)

      expect(mock_client).to have_received(:get_token).with(
        refresh_token,
        grant_type: 'refresh_token'
      )

      expected_tokens = {
        'access_token' => 'new_access_token_456',
        'refresh_token' => 'new_refresh_token_456',
        'expires_at' => 9876543210
      }

      expect(result).to eq(expected_tokens)
      expect(user.reload.credentials).to eq(
        'existing' => 'data',
        'test_plugin' => expected_tokens
      )
    end

    it 'passes refresh_token_params to the OAuth client' do
      allow(service).to receive(:refresh_token_params).and_return({ client_id: 'test_id' })

      service.refresh_access_token(refresh_token, user)

      expect(mock_client).to have_received(:get_token).with(
        refresh_token,
        grant_type: 'refresh_token',
        client_id: 'test_id'
      )
    end
  end

  describe '#extract_token_data' do
    it 'extracts standard token data and filters nil values' do
      mock_token = instance_double(OAuth2::AccessToken, token: 'access_token_123', refresh_token: nil, expires_at: 1234567890)
      result = service.send(:extract_token_data, mock_token)
      expect(result).to eq(mock_token_data)
    end
  end
end
