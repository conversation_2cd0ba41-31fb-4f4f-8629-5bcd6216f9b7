# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OauthService::Notion do
  let(:service) { described_class.new('notion') }

  before do
    allow(Rails.application.credentials).to receive(:plugins).and_return({
      notion: { client_id: 'notion_client_id', client_secret: 'notion_client_secret' }
    })
  end

  describe 'Notion-specific configuration' do
    it 'sets correct API endpoints and version' do
      expect(service.send(:oauth_site)).to eq('https://api.notion.com')
      expect(service.send(:oauth_authorize_url)).to eq('/v1/oauth/authorize')
      expect(service.send(:oauth_token_url)).to eq('/v1/oauth/token')
      expect(described_class::API_VERSION).to eq('2022-06-28')
    end

    it 'includes Notion-specific authorization and token parameters' do
      expect(service.send(:authorization_params)).to eq({ owner: 'user' })
      expect(service.send(:token_params)).to eq({ headers: { 'Notion-Version' => '2022-06-28' } })
    end
  end

  describe '#extract_token_data' do
    let(:base_token_attrs) { { token: 'access_token_123', refresh_token: 'refresh_token_123', expires_at: 1234567890 } }

    it 'extracts Notion-specific workspace data' do
      mock_token = instance_double(OAuth2::AccessToken, **base_token_attrs, params: {
        'workspace_name' => 'My Workspace', 'workspace_id' => 'workspace_123', 'bot_id' => 'bot_123'
      })

      result = service.send(:extract_token_data, mock_token)

      expect(result).to include('workspace_name' => 'My Workspace', 'workspace_id' => 'workspace_123', 'bot_id' => 'bot_123')
    end

    it 'filters out nil workspace data' do
      mock_token = instance_double(OAuth2::AccessToken, token: 'access_token_123', refresh_token: nil, expires_at: nil, params: {
        'workspace_name' => nil, 'workspace_id' => 'workspace_123', 'bot_id' => nil
      })

      result = service.send(:extract_token_data, mock_token)

      expect(result).to eq('access_token' => 'access_token_123', 'workspace_id' => 'workspace_123')
    end
  end
end
