require 'rails_helper'

RSpec.describe API::PluginSettingsController, type: :request do
  let(:user) { create(:user) }
  let(:plugin) { Plugin.find_by_keyname('notion') || create(:plugin, keyname: 'notion', name: 'Notion') }
  let(:plugin_setting) { build_stubbed(:plugin_setting, plugin_id: plugin.id, user: user, id: 123) }

  before do
    sign_in(user)
    allow_any_instance_of(API::PluginSettingsController).to receive(:set_plugin_setting) do |controller|
      controller.instance_variable_set(:@plugin_setting, plugin_setting)
    end
    allow(plugin_setting).to receive(:encrypted_settings).and_return({})
  end

  describe 'POST #xhr_select_search' do
    let(:base_params) do
      {
        function: 'notion_database_id',
        query: 'test query',
        plugin_setting: { id: plugin_setting.id }
      }
    end

    context 'when user has notion credentials' do
      let(:search_results) do
        [
          { 'id' => 'db-123', 'name' => 'Project Tasks' },
          { 'id' => 'db-456', 'name' => 'Team Goals' }
        ]
      end

      before do
        user.update(credentials: { 'notion' => { 'access_token' => 'test-token' } })
        allow(Plugins::Notion).to receive(:search_databases).and_return(search_results)
      end

      it 'searches databases and returns results' do
        post '/api/plugin_settings/xhr_select_search', params: base_params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(search_results)
        expect(Plugins::Notion).to have_received(:search_databases)
          .with(user.credentials, 'test query')
      end

      it 'passes empty query when not provided' do
        post '/api/plugin_settings/xhr_select_search', params: base_params.except(:query)

        expect(Plugins::Notion).to have_received(:search_databases)
          .with(user.credentials, '')
      end
    end

    context 'when searching pages' do
      let(:page_results) do
        [
          { 'id' => 'page-789', 'name' => 'Company Wiki' },
          { 'id' => 'page-101', 'name' => 'Meeting Notes' }
        ]
      end

      before do
        user.update(credentials: { 'notion' => { 'access_token' => 'test-token' } })
        allow(Plugins::Notion).to receive(:search_pages).and_return(page_results)
      end

      it 'searches pages when function is notion_page_id' do
        params = base_params.merge(function: 'notion_page_id')
        post '/api/plugin_settings/xhr_select_search', params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(page_results)
        expect(Plugins::Notion).to have_received(:search_pages)
          .with(user.credentials, 'test query')
      end
    end

    context 'when user lacks notion credentials' do
      before do
        user.update(credentials: {})
      end

      it 'returns empty array for database search' do
        post '/api/plugin_settings/xhr_select_search', params: base_params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq([])
      end

      it 'returns empty array for page search' do
        params = base_params.merge(function: 'notion_page_id')
        post '/api/plugin_settings/xhr_select_search', params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq([])
      end
    end

    context 'when plugin setting has encrypted settings' do
      let(:encrypted_notion_creds) { { 'notion' => { 'access_token' => 'encrypted-token' } } }
      let(:search_results) { [{ 'id' => 'db-999', 'name' => 'Private Database' }] }

      before do
        allow(plugin_setting).to receive(:encrypted_settings).and_return(encrypted_notion_creds)
        allow(Plugins::Notion).to receive(:search_databases).and_return(search_results)
      end

      it 'merges plugin setting credentials with user credentials' do
        user.update(credentials: { 'other_service' => { 'token' => 'user-token' } })

        post '/api/plugin_settings/xhr_select_search', params: base_params

        expected_credentials = {
          'other_service' => { 'token' => 'user-token' },
          'notion' => { 'access_token' => 'encrypted-token' }
        }

        expect(Plugins::Notion).to have_received(:search_databases)
          .with(expected_credentials, 'test query')
      end
    end

    context 'with unknown function' do
      it 'returns empty array' do
        params = base_params.merge(function: 'unknown_function')
        post '/api/plugin_settings/xhr_select_search', params: params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq([])
      end
    end

    context 'when not authenticated' do
      before { sign_out(user) }

      it 'redirects to login' do
        post '/api/plugin_settings/xhr_select_search', params: base_params

        expect(response).to have_http_status(:redirect)
      end
    end

    context 'when plugin setting belongs to different user' do
      let(:other_user) { create(:user) }
      let(:other_plugin_setting) { build_stubbed(:plugin_setting, plugin_id: plugin.id, user: other_user, id: 456) }

      it 'uses current user credentials regardless of plugin_setting param' do
        user.update(credentials: { 'notion' => { 'access_token' => 'user-token' } })
        allow(Plugins::Notion).to receive(:search_databases).and_return([])

        params = base_params.merge(plugin_setting: { id: other_plugin_setting.id })
        post '/api/plugin_settings/xhr_select_search', params: params

        expect(Plugins::Notion).to have_received(:search_databases)
          .with(user.credentials, 'test query')
      end
    end
  end
end
