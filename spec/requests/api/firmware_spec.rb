require 'rails_helper'

RSpec.describe "API::Firmware", type: :request do
  describe "GET /latest" do
    it "returns nothing if no firmware is in stable release condition" do
      get latest_api_firmware_index_path

      expect(response).to have_http_status(200)
      expect(response.parsed_body).to eql({ 'url' => nil, 'version' => nil, 'merged_path' => '/firmware/trmnl/FW1.6.0.rc1.bin' })
    end

    it "returns stable release" do
      create(:firmware, environment: 'development', version: '0.0.1')
      create(:firmware, environment: 'staging', version: '0.0.2')
      stable_firmware = create(:firmware, environment: 'production')

      get latest_api_firmware_index_path

      expect(response).to have_http_status(200)
      expect(response.parsed_body[:url]).to eql(stable_firmware.url)
      expect(response.parsed_body[:version]).to eql(stable_firmware.version)
    end
  end
end
