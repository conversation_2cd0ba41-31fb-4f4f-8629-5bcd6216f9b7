require 'swagger_helper'

describe 'device models API' do
  path '/models' do
    get 'List all device models' do
      tags 'Models'
      include_context 'public API request defaults'

      response '200', 'Success' do
        schema type: :object, properties: { data: { type: :array, items: { '$ref' => '#/components/schemas/Model' } } }

        run_test! do
          hidden_model_names = DeviceModel.hidden.pluck(:keyname)
          expect(response.parsed_body['data'].map { |m| m['name'] }).not_to include(*hidden_model_names)
        end
      end
    end
  end
end
