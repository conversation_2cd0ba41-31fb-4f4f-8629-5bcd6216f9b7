require 'rails_helper'

RSpec.describe Plugins::PluginsController, type: :request do
  let(:user) { create(:user) }
  let!(:plugin1) { create(:plugin, keyname: 'notion', name: 'Notion', active: true, plugin_type: 'native') }
  let!(:plugin2) { create(:plugin, keyname: 'google_calendar', name: 'Google Calendar', active: true, plugin_type: 'native') }
  let!(:inactive_plugin) { create(:plugin, keyname: 'inactive', name: 'Inactive', active: false, plugin_type: 'native') }

  before { sign_in(user) }

  describe 'GET #demo' do
    before do
      stub_const('Plugins::PluginsController::LOCALS', {
        notion: {
          variants: {
            database: { instance_name: 'Project Dashboard' },
            page: { instance_name: 'Company Wiki' }
          }
        },
        google_calendar: { instance_name: 'My Calendar' }
      })
    end

    it 'renders demo template with plugin variants' do
      get '/plugins/demo'

      expect(response).to have_http_status(:ok)
      expect(response).to render_template('plugins/demo')
    end

    it 'expands plugins with variants into separate entries for UI display' do
      get '/plugins/demo'

      plugin_variants = assigns(:plugin_variants)

      # Notion should be expanded into two variants for UI
      notion_variants = plugin_variants.select { |v| v[:keyname] == 'notion' }
      expect(notion_variants.size).to eq(2)

      # Google Calendar should remain as single entry
      calendar_variants = plugin_variants.select { |v| v[:keyname] == 'google_calendar' }
      expect(calendar_variants.size).to eq(1)
    end

    it 'formats display names with variant type for UI' do
      get '/plugins/demo'

      plugin_variants = assigns(:plugin_variants)
      notion_database = plugin_variants.find { |v| v[:keyname] == 'notion' && v[:variant_type] == :database }

      expect(notion_database[:display_name]).to eq('notion (database)')
    end

    it 'only includes active native plugins' do
      get '/plugins/demo'

      plugin_variants = assigns(:plugin_variants)
      keynames = plugin_variants.map { |v| v[:keyname] }

      expect(keynames).to include('notion', 'google_calendar')
      expect(keynames).not_to include('inactive')
    end
  end

  describe 'GET #multi' do
    it 'accepts variant parameter for multi-plugin rendering' do
      get '/plugins/multi', params: {
        plugins: 'notion,google_calendar',
        variant: 'database'
      }

      expect(response).to have_http_status(:ok)
    end

    it 'handles missing variant parameter' do
      get '/plugins/multi', params: { plugins: 'notion,google_calendar' }

      expect(response).to have_http_status(:ok)
    end
  end

  describe 'GET #show' do
    it 'accepts variant parameter for single plugin rendering' do
      get '/plugins/notion', params: { variant: 'database' }

      expect(response).to have_http_status(:ok)
    end

    it 'handles missing variant parameter' do
      get '/plugins/notion'

      expect(response).to have_http_status(:ok)
    end
  end
end
