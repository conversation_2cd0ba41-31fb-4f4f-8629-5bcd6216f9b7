require 'rails_helper'

RSpec.describe Previewable, type: :controller do
  include Devise::Test::ControllerHelpers

  controller(ApplicationController) do
    include Previewable
    include Demoable

    def test_demo_html_document
      demo_html_document
    end

    private

    def render_plugin_html(locale, template:, locals:)
      render json: {
        locale: locale,
        template: template,
        locals: locals
      }
    end
  end

  before do
    routes.draw { get 'test_demo_html_document' => 'anonymous#test_demo_html_document' }
    allow(I18n).to receive(:default_locale).and_return(:en)
  end

  describe '#demo_html_document' do
    let(:plugin_config) do
      {
        instance_name: 'Test Plugin',
        data: 'sample data'
      }
    end

    before do
      stub_const('Demoable::LOCALS', { test_plugin: plugin_config })
    end

    context 'when no variant parameter is provided' do
      it 'calls get_demo_data without variant and renders plugin template' do
        get :test_demo_html_document, params: { id: 'test_plugin' }

        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)

        expect(response_body['locale']).to eq('en')
        expect(response_body['template']).to eq('plugins/test_plugin/full')
        expect(response_body['locals']).to include({
          'instance_name' => 'Test Plugin',
          'data' => 'sample data',
          'no_screen_padding' => nil
        })
      end
    end

    context 'when variant parameter is provided' do
      let(:variant_config) do
        {
          variants: {
            database: {
              instance_name: 'Database View',
              display_type: 'database'
            },
            page: {
              instance_name: 'Page View',
              display_type: 'page'
            }
          }
        }
      end

      before do
        stub_const('Demoable::LOCALS', { notion: variant_config })
      end

      it 'passes variant to get_demo_data and renders with variant data' do
        get :test_demo_html_document, params: { id: 'notion', variant: 'database' }

        expect(response).to have_http_status(:ok)
        response_body = JSON.parse(response.body)

        expect(response_body['template']).to eq('plugins/notion/full')
        expect(response_body['locals']).to include({
          'instance_name' => 'Database View',
          'display_type' => 'database',
          'no_screen_padding' => nil
        })
      end

      it 'handles different variant types' do
        get :test_demo_html_document, params: { id: 'notion', variant: 'page' }

        response_body = JSON.parse(response.body)
        expect(response_body['locals']).to include({
          'instance_name' => 'Page View',
          'display_type' => 'page'
        })
      end
    end

    context 'when no_screen_padding parameter is provided' do
      it 'merges no_screen_padding into locals' do
        get :test_demo_html_document, params: {
          id: 'test_plugin',
          no_screen_padding: 'true'
        }

        response_body = JSON.parse(response.body)
        expect(response_body['locals']).to include({
          'no_screen_padding' => 'true'
        })
      end

      it 'works together with variant parameter' do
        variant_config = {
          variants: {
            database: { instance_name: 'Database View' }
          }
        }
        stub_const('Demoable::LOCALS', { notion: variant_config })

        get :test_demo_html_document, params: {
          id: 'notion',
          variant: 'database',
          no_screen_padding: 'false'
        }

        response_body = JSON.parse(response.body)
        expect(response_body['locals']).to include({
          'instance_name' => 'Database View',
          'no_screen_padding' => 'false'
        })
      end
    end

    context 'when plugin does not exist' do
      it 'renders with empty locals' do
        get :test_demo_html_document, params: { id: 'nonexistent_plugin' }

        response_body = JSON.parse(response.body)
        expect(response_body['locals']).to eq({
          'no_screen_padding' => nil
        })
      end
    end

    context 'when variant does not exist' do
      let(:variant_config) do
        {
          variants: {
            database: { instance_name: 'Database View' }
          }
        }
      end

      before do
        stub_const('Demoable::LOCALS', { notion: variant_config })
      end

      it 'renders with empty variant data' do
        get :test_demo_html_document, params: {
          id: 'notion',
          variant: 'nonexistent_variant'
        }

        response_body = JSON.parse(response.body)
        expect(response_body['locals']).to eq({
          'no_screen_padding' => nil
        })
      end
    end
  end
end
