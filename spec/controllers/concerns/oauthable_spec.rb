# frozen_string_literal: true

require 'rails_helper'

RSpec.describe <PERSON><PERSON>hab<PERSON>, type: :controller do
  controller(ApplicationController) do
    include <PERSON><PERSON><PERSON><PERSON>

    def test_process_oauth_callback
      process_oauth_callback(params, current_user)
      render json: { success: true }
    end
  end

  let(:user) { create(:user, credentials: { 'existing' => 'data' }) }
  let(:oauth_params) { { id: 'notion', code: 'oauth_code_123' } }
  let(:mock_oauth_service) { instance_double(OauthService::Notion) }

  before do
    allow(controller).to receive(:current_user).and_return(user)
    routes.draw { post 'test_process_oauth_callback' => 'anonymous#test_process_oauth_callback' }
    allow(Rails.application.credentials).to receive(:plugins).and_return({
      notion: { client_id: 'notion_client_id', client_secret: 'notion_client_secret' }
    })
  end

  describe '#process_oauth_callback' do
    context 'when plugin uses OAuth2 service' do
      before do
        allow(controller).to receive(:oauth2_service_for).with('notion').and_return(mock_oauth_service)
        allow(mock_oauth_service).to receive(:process_callback)
      end

      it 'uses OAuth2 service to process callback' do
        post :test_process_oauth_callback, params: oauth_params
        expect(mock_oauth_service).to have_received(:process_callback).with('oauth_code_123', user)
      end

      it 'skips legacy OAuth processing when OAuth2 service is found' do
        allow(Plugins::Basecamp).to receive(:fetch_access_token)
        post :test_process_oauth_callback, params: oauth_params

        expect(mock_oauth_service).to have_received(:process_callback)
        expect(Plugins::Basecamp).not_to have_received(:fetch_access_token)
      end
    end

    context 'when plugin falls back to legacy OAuth' do
      let(:legacy_params) { { id: 'basecamp', code: 'oauth_code_123' } }
      let(:legacy_tokens) { { access_token: 'basecamp_token', refresh_token: 'basecamp_refresh' } }

      before do
        allow(controller).to receive(:oauth2_service_for).with('basecamp').and_return(nil)
        allow(Plugins::Basecamp).to receive(:fetch_access_token).and_return(legacy_tokens)
      end

      it 'processes legacy OAuth when no OAuth2 service exists' do
        post :test_process_oauth_callback, params: legacy_params

        expect(Plugins::Basecamp).to have_received(:fetch_access_token).with('oauth_code_123')
        expect(user.reload.credentials['basecamp']).to eq(legacy_tokens.stringify_keys)
      end
    end
  end

  describe '#oauth2_service_for' do
    it 'returns OAuth service instance for existing service' do
      service = controller.send(:oauth2_service_for, 'notion')
      expect(service).to be_a(OauthService::Notion)
      expect(service.plugin_keyname).to eq('notion')
    end

    it 'returns nil for non-existent service' do
      service = controller.send(:oauth2_service_for, 'non_existent')
      expect(service).to be_nil
    end

    it 'handles service creation errors gracefully' do
      test_service_class = Class.new(OauthService::Base) do
        def initialize(_plugin_keyname)
          super
          raise StandardError, 'Configuration error'
        end
      end
      stub_const('OauthService::TestPlugin', test_service_class)

      service = controller.send(:oauth2_service_for, 'test_plugin')
      expect(service).to be_nil
    end
  end
end
