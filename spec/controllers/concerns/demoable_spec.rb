require 'rails_helper'

RSpec.describe Demoable, type: :controller do
  include Devise::Test::ControllerHelpers

  controller(ApplicationController) do
    include Demoable

    def test_get_demo_data
      keyname = params[:keyname]
      variant_type = params[:variant_type]
      render json: get_demo_data(keyname, variant_type)
    end
  end

  before do
    routes.draw { get 'test_get_demo_data' => 'anonymous#test_get_demo_data' }
  end

  describe '#get_demo_data' do
    context 'when plugin has no variants' do
      let(:plugin_config) do
        {
          instance_name: 'Test Plugin',
          data: 'sample data'
        }
      end

      before do
        stub_const('Demoable::LOCALS', { test_plugin: plugin_config })
      end

      it 'returns plugin config directly when no variant_type provided' do
        get :test_get_demo_data, params: { keyname: 'test_plugin' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
          'instance_name' => 'Test Plugin',
          'data' => 'sample data'
        })
      end

      it 'returns plugin config directly when variant_type provided but no variants exist' do
        get :test_get_demo_data, params: { keyname: 'test_plugin', variant_type: 'database' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
          'instance_name' => 'Test Plugin',
          'data' => 'sample data'
        })
      end
    end

    context 'when plugin has variants' do
      let(:plugin_config) do
        {
          variants: {
            database: {
              instance_name: 'Database View',
              display_type: 'database',
              items: ['item1', 'item2']
            },
            page: {
              instance_name: 'Page View',
              display_type: 'page',
              content: 'page content'
            }
          }
        }
      end

      before do
        stub_const('Demoable::LOCALS', { notion: plugin_config })
      end

      it 'returns specific variant when variant_type matches' do
        get :test_get_demo_data, params: { keyname: 'notion', variant_type: 'database' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
          'instance_name' => 'Database View',
          'display_type' => 'database',
          'items' => ['item1', 'item2']
        })
      end

      it 'returns different variant when different variant_type provided' do
        get :test_get_demo_data, params: { keyname: 'notion', variant_type: 'page' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
          'instance_name' => 'Page View',
          'display_type' => 'page',
          'content' => 'page content'
        })
      end

      it 'returns first variant when no variant_type provided' do
        get :test_get_demo_data, params: { keyname: 'notion' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
          'instance_name' => 'Database View',
          'display_type' => 'database',
          'items' => ['item1', 'item2']
        })
      end

      it 'returns empty hash when variant_type does not match any variant' do
        get :test_get_demo_data, params: { keyname: 'notion', variant_type: 'nonexistent' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({})
      end
    end

    context 'when plugin config does not exist' do
      before do
        stub_const('Demoable::LOCALS', {})
      end

      it 'returns empty hash' do
        get :test_get_demo_data, params: { keyname: 'nonexistent_plugin' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({})
      end
    end

    context 'when plugin has empty variants hash' do
      let(:plugin_config) do
        {
          variants: {},
          fallback_data: 'should not be returned'
        }
      end

      before do
        stub_const('Demoable::LOCALS', { empty_variants: plugin_config })
      end

      it 'returns empty hash when no variants exist' do
        get :test_get_demo_data, params: { keyname: 'empty_variants', variant_type: 'any' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({})
      end

      it 'returns empty hash when no variant_type provided' do
        get :test_get_demo_data, params: { keyname: 'empty_variants' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({})
      end
    end

    context 'when keyname is symbol' do
      let(:plugin_config) do
        {
          instance_name: 'Symbol Key Test',
          data: 'works with symbols'
        }
      end

      before do
        stub_const('Demoable::LOCALS', { symbol_test: plugin_config })
      end

      it 'handles symbol keynames correctly' do
        get :test_get_demo_data, params: { keyname: :symbol_test }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq({
          'instance_name' => 'Symbol Key Test',
          'data' => 'works with symbols'
        })
      end
    end
  end
end
