# This file is copied to spec/ when you run 'rails generate rspec:install'

require 'simplecov'
require 'simplecov-tailwindcss'
require 'active_job'

SimpleCov.start 'rails' do
  add_group "Services", "app/services"

  add_filter 'vendor'
  add_filter 'app/channels/application_cable'
  add_filter 'app/admin'
end

SimpleCov.formatter = SimpleCov::Formatter::TailwindFormatter

require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'

# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?
require 'rspec/rails'

require 'sidekiq/testing'
Sidekiq::Testing.inline!

# https://dev.to/adrianvalenz/setup-rspec-on-a-fresh-rails-7-project-5gp
require_relative 'support/factory_bot'
require_relative 'support/chrome'
Dir[Rails.root.join('spec/spec_helpers/*.rb')].each { |f| require f }

# Add additional requires below this line. Rails is not loaded until this point!

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
# Dir[Rails.root.join('spec', 'support', '**', '*.rb')].sort.each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  puts e.to_s.strip
  exit 1
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

ActiveJob::Base.queue_adapter = :test

RSpec.configure do |config|
  config.include Devise::Test::IntegrationHelpers, type: :request
  config.include Devise::Test::IntegrationHelpers, type: :feature

  config.fixture_paths = [Rails.root.join("spec/fixtures")]
  config.use_transactional_fixtures = true
  config.infer_spec_type_from_file_location!
  config.filter_rails_from_backtrace!

  config.before(:suite) do
    Rails.application.load_seed # for Settings, etc

    # workaround for https://github.com/heartcombo/devise/issues/5774, https://github.com/heartcombo/devise/issues/5705
    Rails.application.try(:reload_routes_unless_loaded)

    # prevent issues with third_party plugins being created during test runs
    ActiveRecord::Base.connection.tables.each do |t|
      ActiveRecord::Base.connection.reset_pk_sequence!(t)
    end
  end
end
