require 'rails_helper'

RSpec.describe Plugin, type: :model do
  describe 'native plugins' do
    let(:plugins) do
      Plugin.native.where('name is not null')
    end

    it 'expects plugin categories to be organized alphabetically' do
      expect(Plugin::CATEGORIES).to eql Plugin::CATEGORIES.sort
    end

    it 'expects every plugin to have a name and description' do
      plugins.each do |plugin|
        expect(plugin.name).to be_present
        expect(plugin.description).to be_present
      end
    end

    it 'expects every plugin to have a valid refresh_every' do
      plugins.where.not(keyname: ['alias', 'redirect']).each do |plugin|
        expect(plugin.refresh_every).to be_between(0, 1440)
      end
    end

    it 'expects preview image to exist' do
      plugins.each do |plugin|
        next unless plugin.active

        bmp_path = "plugin_previews/#{plugin.keyname}.bmp"
        png_path = "plugin_previews/#{plugin.keyname}.png"
        expect(Rails.application.precompiled_assets).to include(bmp_path).or include(png_path)
      end
    end

    it 'expects all Oauth plugins to have a redirect url' do
      allow_any_instance_of(Plugin).to receive(:token).and_return 'token' # prevent external server pings to instantiate redirect URLs

      plugins.where(form_type: ['oauth2']).each do |plugin|
        expect(plugin.redirect_url).to_not be_nil
      end
    end

    it 'expects plugins to follow conventions' do
      plugins.each do |plugin|
        # description
        expect(plugin.description.length).to be <= 35, "Plugin ##{plugin.id} has too long a description: #{plugin.description}"
        expect(plugin.description.titleize).to_not eql plugin.description # expect 'capitalize()' equivalent, but not testing for this given exceptions like SaaS
        expect(plugin.description).to_not eql plugin.name
        expect(plugin.description).to_not include('.')

        # categories
        plugin.category.each { |cat| expect(Plugin::CATEGORIES).to include(cat), "Plugin ##{plugin.id} includes a non-approved category: #{plugin.category}" }
      end
    end

    it 'expects every link to documentation to be https' do
      plugins.each do |plugin|
        next if plugin.knowledge_base_url.nil?

        expect(plugin.knowledge_base_url).to include('https://')
      end
    end

    it 'should allow hiding title bar unless global' do
      plugins.each do |plugin|
        if plugin.global? || plugin.private?
          expect(plugin.should_observe_title_bar_preferences?).to be false
        elsif plugin.native?
          expect(plugin.should_observe_title_bar_preferences?).to be true
        end
      end
    end

    ## TODO: Account for plugins with specific Light & Dark logo variants
    #
    # it 'expects only 1 logo image per plugin' do
    #   plugin_images_path = File.expand_path('public/images/plugins')
    #   files = Dir.glob("#{plugin_images_path}/*.*")
    #   expect(files.count).to eql(Plugin.count)
    # end
  end

  describe 'third_party plugins' do
    subject { create(:plugin, :third_party) }

    it 'defaults new plugins to development status' do
      expect(subject.status).to eql 'development'
    end

    it 'derives keyname from name' do
      expect(subject.keyname).to eql subject.name.downcase.parameterize(separator: '_')
    end

    it 'generates client_id and client_secret' do
      expect(subject.client_id).to_not be_nil
      expect(subject.client_secret).to_not be_nil
    end

    it 'should allow hiding title bar' do
      expect(subject.should_observe_title_bar_preferences?).to be true
    end

    context '#template_folder' do
      it 'returns template_folder path' do
        expect(subject.template_folder).to eq('private_plugin')
      end
    end
  end

  describe 'OAuth2 functionality' do
    let(:oauth2_plugin) do
      create(:plugin, form_type: 'oauth2', name: 'Notion', keyname: 'notion')
    end

    describe '#oauth2?' do
      it 'identifies OAuth2 plugins correctly' do
        expect(oauth2_plugin.oauth2?).to be true

        non_oauth2_plugin = create(:plugin, form_type: 'form_input')
        expect(non_oauth2_plugin.oauth2?).to be false
      end
    end
  end

  describe 'scope' do
    describe '#mashup_incompatible' do
      it { expect(described_class.mashup_incompatible.count).to eq(5) }
    end
  end
end
