RSpec.shared_context 'device API defaults', shared_context: :metadata do
  consumes 'application/json'
  produces 'application/json'
end

RSpec.shared_context 'public API request defaults', shared_context: :metadata do
  consumes 'application/json'
  produces 'application/json'
end

RSpec.shared_context 'API request defaults', shared_context: :metadata do
  consumes 'application/json'
  produces 'application/json'
  security [bearer_auth: []]
end

RSpec.shared_examples 'an authorized API' do
  response '401', 'Unauthorized' do
    schema '$ref' => '#/components/schemas/Error'

    context 'when no authentication token is provided' do
      let(:Authorization) { '' }

      run_test! do |response|
        expect(response.parsed_body).to eql({ 'error' => 'Invalid API key' })
      end
    end

    context 'when an invalid authentication token is provided' do
      let(:Authorization) { "invalid-token" }

      run_test! do |response|
        expect(response.parsed_body).to eql({ 'error' => 'Invalid API key' })
      end
    end
  end
end
