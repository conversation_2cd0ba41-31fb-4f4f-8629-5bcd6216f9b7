# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_28_162220) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"
  enable_extension "uuid-ossp"

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "announcements", force: :cascade do |t|
    t.string "title"
    t.text "body"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "blog_posts", force: :cascade do |t|
    t.string "title"
    t.string "slug"
    t.text "description"
    t.boolean "draft", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "author"
    t.string "author_title"
    t.index ["slug"], name: "index_blog_posts_on_slug"
  end

  create_table "bounties", force: :cascade do |t|
    t.string "title"
    t.string "description"
    t.string "url"
    t.integer "amount"
    t.string "completed_by"
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "device_models", force: :cascade do |t|
    t.string "keyname"
    t.integer "width"
    t.integer "height"
    t.integer "colour_depth"
    t.string "format"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "colours"
    t.float "scale_factor", default: 1.0
    t.integer "offset_x", default: 0
    t.integer "offset_y", default: 0
    t.integer "rotate", default: 0
    t.integer "device_type", default: 0
    t.string "name"
    t.jsonb "css", default: {}
    t.datetime "published_at"
    t.text "description"
    t.boolean "visible", default: true, null: false
    t.index ["keyname"], name: "index_device_models_on_keyname", unique: true
    t.index ["published_at"], name: "index_device_models_on_published_at"
    t.index ["visible"], name: "index_device_models_on_visible"
  end

  create_table "devices", force: :cascade do |t|
    t.string "name"
    t.string "api_key"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "friendly_id"
    t.datetime "api_key_updated_at"
    t.string "mac_address"
    t.integer "refresh_interval"
    t.boolean "identification_requested", default: false
    t.integer "visibility", default: 0
    t.string "mirror"
    t.float "battery_voltage"
    t.string "firmware_version"
    t.datetime "deleted_at"
    t.boolean "sleep_mode_enabled", default: false, null: false
    t.datetime "setup_at"
    t.boolean "reset_requested", default: false, null: false
    t.integer "rssi"
    t.boolean "accepts_beta_firmware", default: false, null: false
    t.string "special_function"
    t.datetime "developer_edition_unlocked_at"
    t.boolean "ota_enabled", default: true, null: false
    t.string "serial"
    t.integer "sleep_start_time"
    t.integer "sleep_end_time"
    t.bigint "partner_action_id"
    t.boolean "sleep_screen_enabled", default: false, null: false
    t.string "external_order_id"
    t.integer "width"
    t.integer "height"
    t.integer "model_id", default: 3
    t.boolean "low_battery_notification_enabled", default: false, null: false
    t.datetime "low_battery_notification_sent_at"
    t.integer "playlist_groups_count", default: 0
    t.boolean "mfa_reset_requested", default: false, null: false
    t.integer "playlist_item_ttl"
    t.bigint "guest_mode_item_id"
    t.integer "guest_mode_item_duration"
    t.index ["api_key"], name: "index_devices_on_api_key", where: "(deleted_at IS NULL)"
    t.index ["deleted_at"], name: "index_devices_on_deleted_at"
    t.index ["guest_mode_item_id"], name: "index_devices_on_guest_mode_item_id"
    t.index ["mirror"], name: "index_devices_on_mirror"
    t.index ["user_id"], name: "index_devices_on_user_id"
  end

  create_table "firmwares", force: :cascade do |t|
    t.string "url", null: false
    t.string "version", null: false
    t.integer "environment"
    t.bigint "released_by"
    t.datetime "released_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["environment"], name: "index_firmwares_on_environment"
  end

  create_table "flipper_features", force: :cascade do |t|
    t.string "key", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_flipper_features_on_key", unique: true
  end

  create_table "flipper_gates", force: :cascade do |t|
    t.string "feature_key", null: false
    t.string "key", null: false
    t.text "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["feature_key", "key", "value"], name: "index_flipper_gates_on_feature_key_and_key_and_value", unique: true
  end

  create_table "guides", force: :cascade do |t|
    t.string "title"
    t.string "handle"
    t.text "markdown"
    t.text "html"
    t.string "repo_url"
    t.string "github_path"
    t.jsonb "images"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "description"
  end

  create_table "logs", force: :cascade do |t|
    t.integer "source"
    t.jsonb "dump"
    t.bigint "device_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "level", default: 1, null: false
    t.bigint "plugin_setting_id"
    t.index "(((dump -> 0) ->> 'firmware_version'::text))", name: "index_logs_on_firmware_version"
    t.index ["created_at"], name: "index_logs_on_created_at"
    t.index ["device_id", "id"], name: "index_logs_on_device_id_and_id"
    t.index ["dump"], name: "index_logs_on_dump", using: :gin
    t.index ["level"], name: "index_logs_on_level"
    t.index ["plugin_setting_id"], name: "index_logs_on_plugin_setting_id"
    t.index ["user_id", "device_id", "source"], name: "index_logs_on_user_id_and_device_id_and_source"
    t.index ["user_id", "id"], name: "index_logs_on_user_id_and_id"
  end

  create_table "mashup_contents", force: :cascade do |t|
    t.bigint "mashup_id"
    t.bigint "plugin_setting_id"
    t.string "position"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plugin_setting_id"], name: "index_mashup_contents_on_plugin_setting_id"
  end

  create_table "mashups", force: :cascade do |t|
    t.integer "layout"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.datetime "refresh_at"
    t.datetime "previous_refresh_at"
    t.string "digest"
    t.boolean "image", default: false
    t.text "model_ids", default: [], array: true
    t.integer "refresh_interval", default: 60
    t.integer "mashup_contents_count", default: 0
    t.boolean "job_claimed", default: false
    t.integer "daily_usage", default: 0
    t.index ["refresh_at"], name: "index_mashups_on_refresh_at", where: "(job_claimed = false)"
    t.index ["refresh_at"], name: "index_plugin_settings_on_refresh_at_claimed_mashups", where: "(job_claimed = true)"
  end

  create_table "oauth_token_stores", force: :cascade do |t|
    t.string "code"
    t.string "access_token"
    t.bigint "plugin_setting_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "plugin_id"
    t.index ["access_token"], name: "index_oauth_token_stores_on_access_token", unique: true
    t.index ["code"], name: "index_oauth_token_stores_on_code", unique: true
    t.index ["plugin_setting_id"], name: "index_oauth_token_stores_on_plugin_setting_id"
  end

  create_table "partner_actions", force: :cascade do |t|
    t.bigint "partner_id", null: false
    t.integer "action_type"
    t.jsonb "data_in", default: {}
    t.jsonb "data_generated", default: {}
    t.jsonb "data_out", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["partner_id"], name: "index_partner_actions_on_partner_id"
  end

  create_table "partners", force: :cascade do |t|
    t.string "name"
    t.bigint "plugin_setting_id"
    t.string "access_token"
    t.string "client_id"
    t.bigint "price_rule_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "playlist_groups", force: :cascade do |t|
    t.string "name"
    t.integer "start_time"
    t.integer "end_time"
    t.bigint "device_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "refresh_interval"
    t.index ["device_id", "start_time", "end_time"], name: "index_playlist_groups_on_device_id_and_start_time_and_end_time"
    t.index ["device_id", "start_time"], name: "index_playlist_groups_on_device_id_and_start_time"
    t.index ["device_id"], name: "index_playlist_groups_on_device_id"
  end

  create_table "playlists", force: :cascade do |t|
    t.bigint "device_id"
    t.bigint "plugin_setting_id"
    t.datetime "rendered_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "mirror", default: false
    t.bigint "mashup_id"
    t.bigint "playlist_group_id", null: false
    t.integer "row_order"
    t.boolean "visible", default: true, null: false
    t.bigint "screen_id"
    t.jsonb "schedule", default: {}
    t.integer "duration"
    t.integer "priority", default: 0, null: false
    t.index ["device_id"], name: "index_playlists_on_device_id"
    t.index ["mashup_id"], name: "index_playlists_on_mashup_id", where: "(mashup_id IS NOT NULL)"
    t.index ["playlist_group_id", "rendered_at"], name: "index_playlists_on_playlist_group_id_and_rendered_at"
    t.index ["playlist_group_id", "row_order"], name: "index_playlists_on_playlist_group_id_and_row_order"
    t.index ["playlist_group_id", "row_order"], name: "index_playlists_on_playlist_group_id_and_row_order_unrendered", where: "(rendered_at IS NULL)"
    t.index ["playlist_group_id"], name: "index_playlists_on_playlist_group_id"
    t.index ["plugin_setting_id"], name: "index_playlists_on_plugin_setting_id"
    t.index ["screen_id"], name: "index_playlists_on_screen_id"
  end

  create_table "plugin_settings", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "plugin_id"
    t.jsonb "settings", default: {}
    t.integer "refresh_interval"
    t.datetime "refresh_at", precision: nil
    t.datetime "previous_refresh_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "persistence_data", default: {}
    t.string "name"
    t.jsonb "encrypted_settings", default: {}
    t.uuid "uuid", default: -> { "uuid_generate_v4()" }, null: false
    t.bigint "master_id"
    t.integer "state", default: 0
    t.string "error_message"
    t.integer "error_retry_count", default: 0
    t.integer "mashup_contents_count", default: 0, null: false
    t.integer "playlists_count", default: 0, null: false
    t.integer "recipe_status"
    t.boolean "image", default: false
    t.text "model_ids", default: [], array: true
    t.boolean "job_claimed", default: false
    t.bigint "upstream_id"
    t.integer "forks_count", default: 0
    t.integer "daily_usage", default: 0
    t.datetime "debug_logs_until"
    t.index ["created_at", "name"], name: "index_plugin_settings_on_created_at_and_name", where: "(recipe_status = 1)"
    t.index ["plugin_id", "master_id"], name: "index_plugin_settings_on_plugin_id_and_master_id"
    t.index ["plugin_id", "refresh_at"], name: "index_plugin_settings_on_active_plugin_settings", where: "((state <> 2) AND ((playlists_count > 0) OR (mashup_contents_count > 0)) AND (job_claimed = false))"
    t.index ["plugin_id", "refresh_at"], name: "index_plugin_settings_on_plugin_id_and_refresh_at"
    t.index ["plugin_id"], name: "index_plugin_settings_on_plugin_id"
    t.index ["refresh_at"], name: "index_plugin_settings_on_refresh_at_claimed_plugin_settings", where: "(job_claimed = true)"
    t.index ["upstream_id"], name: "index_plugin_settings_on_upstream_id"
    t.index ["user_id"], name: "index_plugin_settings_on_user_id"
    t.index ["uuid"], name: "index_plugin_settings_on_uuid", unique: true
  end

  create_table "plugins", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.boolean "active"
    t.boolean "global", default: false
    t.boolean "hide_refresh_interval", default: false
    t.boolean "ignore_persistence_data", default: false
    t.string "keyname"
    t.integer "form_type"
    t.integer "plugin_type"
    t.integer "status"
    t.string "category", default: [], array: true
    t.integer "refresh_every"
    t.string "cron"
    t.string "name_placeholder"
    t.jsonb "account_fields", default: {}
    t.string "image"
    t.string "image_dark"
    t.string "knowledge_base_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "client_id"
    t.string "client_secret"
    t.string "installation_url"
    t.string "plugin_management_url"
    t.string "uninstallation_webhook_url"
    t.string "installation_success_webhook_url"
    t.string "plugin_markup_url"
    t.bigint "user_id"
    t.boolean "no_screen_padding", default: false
    t.boolean "disallow_mashup", default: false
    t.string "processor", default: "chrome"
    t.index ["keyname"], name: "index_plugins_on_keyname", unique: true
  end

  create_table "redirect_urls", force: :cascade do |t|
    t.string "path"
    t.string "destination"
    t.bigint "clicks", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "screens", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "plugin_setting_id"
    t.bigint "user_id"
    t.datetime "render_at"
    t.datetime "rendered_at"
    t.bigint "device_id"
    t.integer "display_count", default: 0
    t.bigint "mashup_id"
    t.integer "model_id"
    t.index ["device_id", "model_id"], name: "index_screens_on_device_id_and_model_id", unique: true
    t.index ["mashup_id", "user_id"], name: "index_screens_on_mashup_id_and_user_id", unique: true
    t.index ["plugin_setting_id", "model_id", "user_id"], name: "index_screens_on_plugin_setting_id_and_model_id_and_user_id", unique: true
  end

  create_table "script_tags", force: :cascade do |t|
    t.string "name"
    t.string "code"
    t.boolean "enabled"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "settings", force: :cascade do |t|
    t.string "key"
    t.string "value"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_settings_on_key", unique: true
  end

  create_table "trmnl_display_binaries", force: :cascade do |t|
    t.string "architecture"
    t.string "production_url"
    t.string "staging_url"
    t.string "testing_url"
    t.string "development_url"
    t.text "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "usage_transactions", id: false, force: :cascade do |t|
    t.bigint "user_id"
    t.date "date"
    t.integer "amount", default: 0
    t.integer "transaction_type"
    t.index ["user_id", "date"], name: "index_usage_transactions_on_user_id_and_date"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "admin", default: false
    t.jsonb "credentials", default: {}
    t.string "first_name"
    t.string "last_name"
    t.bigint "current_device_id"
    t.string "tz"
    t.string "external_marketing_id"
    t.string "stripe_customer_id"
    t.string "otp_secret"
    t.integer "consumed_timestep"
    t.boolean "otp_required_for_login"
    t.string "referral_code"
    t.bigint "referral_code_external_id"
    t.string "locale"
    t.string "discord_username"
    t.string "api_key"
    t.boolean "title_bar_enabled", default: true, null: false
    t.string "flipper_groups", default: [], null: false, array: true
    t.string "stripe_subscription_id"
    t.index ["api_key"], name: "index_users_on_api_key", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "devices", "playlists", column: "guest_mode_item_id", on_delete: :nullify
  add_foreign_key "devices", "users"
  add_foreign_key "logs", "plugin_settings"
  add_foreign_key "partner_actions", "partners"
  add_foreign_key "playlist_groups", "devices"
  add_foreign_key "playlists", "playlist_groups"
  add_foreign_key "playlists", "screens", on_delete: :nullify
  add_foreign_key "plugin_settings", "plugin_settings", column: "upstream_id"
end
