---
- id: 1
  name: GitHub Commit Graph
  description: Flex your coding frequency
  active: true
  keyname: github_commit_graph
  form_type: form_input
  plugin_type: native
  category:
  - programming
  refresh_every: 30
  cron: "*/3 * * * *"
  name_placeholder: Personal GitHub
  account_fields:
  - keyname: username
    field_type: string
    name: User Name
    description: Your GitHub user handle to generate commit summary
    help_text: Should be without the @ symbol
    placeholder: username
  image: github.svg
  image_dark: github--dark.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/8797310-github-commit-graph
- id: 2
  name: Days Left This Year
  description: Focus on what matters
  active: true
  global: true
  keyname: days_left_year
  form_type: form_input
  plugin_type: native
  category:
  - life
  refresh_every: 360
  hide_refresh_interval: true
  cron: "*/1 * * * *"
  name_placeholder: '2025'
  account_fields: []
  image: days_left_year.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/8797325-days-left-this-year
- id: 3
  name: Wikipedia Articles
  description: Read something interesting
  active: true
  global: true
  keyname: wiki_random_article
  form_type: form_input
  plugin_type: native
  category:
  - programming
  - life
  - discovery
  refresh_every: 240
  hide_refresh_interval: true
  cron: "*/3 * * * *"
  name_placeholder: Free Encyclopedia
  account_fields: []
  image: wikipedia.svg
- id: 4
  name: Product Hunt
  description: Today's featured products
  active: true
  global: true
  keyname: product_hunt
  form_type: form_input
  plugin_type: native
  category:
  - programming
  - marketing
  - discovery
  refresh_every: 60
  hide_refresh_interval: true
  cron: "*/3 * * * *"
  name_placeholder: Today's launches
  account_fields: []
  image: product_hunt.svg
- id: 5
  name: Stock Price
  description: Stock, Metal & Index prices
  active: true
  keyname: stock_price
  form_type: form_input
  plugin_type: native
  name_placeholder: Stonks
  account_fields:
  - keyname: symbol
    field_type: string
    name: Stock Tickers
    placeholder: GOOG, META
    description: Enter your preferred stock tickers, separated by a comma.
    help_text: Ticker Formats (TICKER, TICKER.EX, EXCHANGE:TICKER)
  - keyname: currency
    field_type: select
    name: Currency
    options:
    - USD
    - CAD
    - CHF
    - CNY
    - EUR
    - GBP
    - INR
    - JPY
    - KRW
    - ZAR
    - CLP
    description: Base price (USD) will be multiplied by <= 1 day old exchange rate.
    default: usd
  - keyname: extended_hours
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Enable extended hours quotes?
    description: If yes, prices after market close will reflect after hours values.
      If no, prices will reflect market close.
    optional: true
  category:
  - news
  - marketing
  refresh_every: 10
  cron: "*/1 * * * *"
  image: stock_price.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-stock-price
- id: 6
  name: Reddit
  description: Top posts from your favorite subs
  active: true
  keyname: reddit
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Subs
  account_fields:
  - keyname: subreddits
    field_type: string
    placeholder: wallstreetbets,perfectfit
    name: Subreddits
    description: Enter your favorite subreddits, separated by a comma
    help_text: For more than 2 we suggest making another reddit connection
  - keyname: sort_by
    field_type: select
    options:
    - Hot
    - New
    - Top
    name: Sort By
    description: Pick a sorting algorithm
    help_text: Hot posts have more recent upvote, Top posts have the most votes regardless
      of age
    default: top
  - keyname: time_period
    field_type: select
    options:
    - Hour
    - Day
    - Week
    - Month
    - Year
    - All
    name: Time Period
    description: Pick a time range of posts to consider.
    optional: true
  - keyname: truncate_title
    field_type: select
    name: Truncate title?
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    description: If yes, title will be shortened but more posts will be shown on your
      device.
    optional: true
  - keyname: include_metadata
    field_type: select
    name: Include metadata?
    options:
    - 'Yes'
    - 'No'
    description: If yes, OP username and vote count will be shown below post title.
    default: 'yes'
    optional: true
  category:
  - life
  - discovery
  refresh_every: 60
  cron: "*/3 * * * *"
  image: reddit.svg
- id: 7
  name: Days Left Until Retirement
  description: Focus on what matters
  active: true
  keyname: days_left_retirement
  form_type: form_input
  plugin_type: native
  category:
  - life
  refresh_every: 360
  hide_refresh_interval: true
  name_placeholder: Relax D-Day
  account_fields:
  - keyname: birthday
    field_type: date
    placeholder: '1990-01-01'
    name: Birthday
    description: Provide your date of birth.
    encrypted: true
  - keyname: retirement_age
    field_type: number
    placeholder: '65'
    name: Retirement Age
    description: At what age do you plan to retire?
  cron: "*/3 * * * *"
  image: days_left_retirement.svg
- id: 8
  name: Google Calendar
  description: Know your upcoming schedule
  active: true
  keyname: google_calendar
  form_type: oauth2
  plugin_type: native
  no_screen_padding: false
  name_placeholder: Personal Calendar
  account_fields:
  - keyname: calendar
    field_type: xhrSelect
    name: Calendar
    description: Select calendars to sync.
    multiple: true
    help_text: Hold cmd or ctrl to choose multiple
  - keyname: event_layout
    field_type: select
    options:
    - Agenda (today only): today_only
    - Default: default
    - Week: week
    - Month: month
    - Rolling Month: rolling_month
    - Schedule (minimal): schedule
    name: Layout
    description: How do you want to visualize events?
    default: default
    conditional_validation:
    - when: week
      hidden:
      - include_event_time
    - when: month
      hidden:
      - scroll_time
      - scroll_time_end
      - fixed_week
    - when: today_only
      hidden:
      - first_day
      - fixed_week
      - scroll_time
      - scroll_time_end
      - include_event_time
    - when: schedule
      hidden:
      - first_day
      - fixed_week
      - scroll_time
      - scroll_time_end
      - include_event_time
    - when: rolling_month
      hidden:
      - scroll_time
      - scroll_time_end
      - fixed_week
  - keyname: time_format
    field_type: select
    options:
    - AM/PM
    - 24 hrs
    default: am/pm
    name: Time Format
    description: Select preferred time format.
  - keyname: date_format
    name: Date Format
    description: Select prefered date heading format. Used in Default + Agenda layouts.
    help_text: Column headings in Week + Month layouts will observe your Locale from the Account tab.
    field_type: select
    options:
    - Localized (short): short
    - Thursday, November 26: '%A, %B %-d'
    - Thu, Nov 26: '%a, %b %-d'
    - 26 November: '%-d %B'
    - November 26: '%B %-d'
    - Nov 26: '%b %-d'
    - 26 Nov: '%-d %b'
    - '11-26': '%-m-%-d'
    - '11/26': '%-m/%-d'
    - '26/11': '%-d/%-m'
    default: short
  - keyname: include_description
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Include description
    description: Whether or not to show a preview of the event details.
  - keyname: include_event_time
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Include event time
    description: Whether or not to show an event's start/end time.
    help_text: Applies to month view only. Default, agenda, and week layouts include
      times by default.
  - keyname: include_past_events
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Include past events?
    description: Whether or not to show events that happened before today. Recommend
      disabling for Month layout.
  - keyname: first_day
    field_type: select
    options:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    default: monday
    name: Week start day
    description: Which day of the week should be the first?
    help_text: Applies to month view and week view (if 'Fixed week' is set to 'Yes').
  - keyname: fixed_week
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Fixed week?
    description: Whether you want to see columns for previous week days.
    help_text: Applies to week view only.
  - keyname: scroll_time
    field_type: time
    name: Fixed start time?
    description: By default, the week's earliest event time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: scroll_time_end
    field_type: time
    name: Fixed end time?
    description: By default, the week's latest event end time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: ignore_phrases
    field_type: text
    name: Ignored Phrases (partial match)
    description: Ignore events with these words or phrases, separated by a comma
    placeholder: busy, personal errand, TODO
    help_text: Event title and description will both be considered.
    optional: true
    encrypted: true
  - keyname: ignore_phrases_exact_match
    field_type: text
    name: Ignored Phrases (exact match)
    description: Ignore events with these words or phrases, separated by a line break
    placeholder: OOO, Wedding Anniversary
    help_text: Event title and description will both be considered.
    optional: true
    encrypted: true
  - keyname: event_status_filter
    field_type: select
    options:
    - All
    - Confirmed Only
    default: all
    name: Event Status Filter
    description: Whether to see all events, or only those you accepted.
  - keyname: zoom_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Zoom mode?
    description: Whether or not to increase event title font sizes and start/end time
      label contrast.
  category:
  - calendar
  - personal
  - productivity
  - life
  refresh_every: 15
  cron: "*/1 * * * *"
  processor: firefox
  image: google-calendar.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9483539-google-calendar
- id: 9
  name: ProfitWell
  description: Your SaaS Dashboard
  active: true
  keyname: profit_well
  form_type: form_input
  plugin_type: native
  name_placeholder: My Company
  account_fields:
  - keyname: api_token
    field_type: string
    name: API Token
    description: Account API Token
    placeholder: 1a2B3c4D5e6F7g8H9i0J1k2L3m4N5o6P
    encrypted: true
  category:
  - kpi
  refresh_every: 15
  cron: "*/6 * * * *"
  image: paddle.svg
- id: 10
  name: Shopify
  description: Shopify store sales dashboard
  active: true
  keyname: shopify
  form_type: form_input
  plugin_type: native
  name_placeholder: My Store
  account_fields:
  - keyname: access_token
    field_type: string
    name: Access Token
    description: Store Access Token
    placeholder: shpat_b04dab04dab04dab04
    encrypted: true
  - keyname: lookback_period
    field_type: select
    options:
    - Last 7 Days
    - Last 30 Days
    - Month To Date
    name: Lookback Period
    description: Time span of displayed order data
    encrypted: false
  - keyname: shop
    field_type: string
    name: Store Web Address
    description: Store subdomain (without 'myshopify')
    placeholder: mystore
    help_text: 'Ex: enter ''usetrmnl'' if your store domain is usetrmnl.myshopify.com'
    encrypted: true
  category:
  - ecommerce
  - kpi
  refresh_every: 60
  cron: "*/6 * * * *"
  processor: chrome
  image: shopify.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9502507-shopify
- id: 11
  name: GitHub Contributors
  description: Repo contributor summary
  active: true
  keyname: github_contributor_summary
  form_type: form_input
  plugin_type: native
  name_placeholder: My Repository
  account_fields:
  - keyname: repo
    field_type: string
    name: Repository
    description: GitHub repository
    placeholder: username/repository
    encrypted: true
  - keyname: fine_grained_token
    field_type: string
    name: Fine Grained Token
    description: Account Fine Grained Access Token
    placeholder: ghp_1a2B3c4D5e6F7g8H9i0J1k2L3m4N5
    encrypted: true
  category:
  - productivity
  - programming
  refresh_every: 1440
  cron: "*/6 * * * *"
  image: github.svg
  image_dark: github--dark.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-github-contributors
- id: 12
  name: Robinhood
  description: Your stock portfolio
  active: false
  keyname: robinhood_portfolio
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Portfolio
  account_fields:
  - keyname: type
    field_type: select
    options:
    - Portfolio Value
    - Value Chart
    name: Type
    description: Select the type of dashboard
  - keyname: lookback_period
    field_type: select
    options:
    - '7'
    - '30'
    - '90'
    name: Type
    description: Time span of displayed data
    help_text: In days
  category:
  - life
  refresh_every: 360
  cron: "*/6 * * * *"
  image: robinhood.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9544311-robinhood
- id: 13
  name: Hacker News
  description: Trending technology content
  active: true
  global: true
  keyname: hacker_news
  form_type: form_input
  plugin_type: native
  account_fields:
  - keyname: story_type
    field_type: select
    options:
    - Top Stories
    - Show HN
    name: Post Type
    description: Select the type of post
  name_placeholder: HN
  category:
  - life
  - news
  - programming
  - discovery
  refresh_every: 15
  hide_refresh_interval: true
  cron: "*/3 * * * *"
  image: hacker-news.svg
- id: 14
  name: Ethereum Wallet
  description: Your ETH portfolio balance
  active: true
  keyname: ethereum_wallet_balance
  form_type: form_input
  plugin_type: native
  name_placeholder: Ethereum Wallet
  account_fields:
  - keyname: address
    field_type: string
    name: Address
    description: Public Wallet Address
    placeholder: 0x1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t
    encrypted: true
  category:
  - life
  - programming
  refresh_every: 15
  cron: "*/6 * * * *"
  image: eth.svg
- id: 15
  name: Bitcoin Wallet
  description: Your BTC portfolio balance
  active: true
  keyname: bitcoin_wallet_balance
  form_type: form_input
  plugin_type: native
  name_placeholder: Bitcoin Wallet
  account_fields:
  - keyname: address
    field_type: string
    name: Address
    description: Public Wallet Address
    placeholder: 8S1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa
    encrypted: true
  category:
  - life
  - programming
  refresh_every: 15
  cron: "*/6 * * * *"
  image: bitcoin.svg
- id: 16
  name: YouTube
  description: Channel and video analytics
  name_placeholder: My Channel
  active: true
  keyname: youtube_analytics
  form_type: oauth2
  plugin_type: native
  account_fields:
  - keyname: duration
    field_type: select
    options:
    - '7'
    - '28'
    - '90'
    - '365'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - marketing
  - kpi
  refresh_every: 30
  cron: "*/3 * * * *"
  image: youtube.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9483658-youtube-analytics
- id: 17
  name: Gumroad
  description: Online storefront analytics
  active: true
  keyname: gumroad_analytics
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Shop
  account_fields:
  - keyname: duration
    field_type: select
    options:
    - '7'
    - '30'
    - '90'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - marketing
  - ecommerce
  - kpi
  refresh_every: 15
  cron: "*/6 * * * *"
  image: gumroad.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-gumroad
- id: 18
  name: Mailchimp
  description: Email newsletter analytics
  active: true
  keyname: mailchimp_analytics
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Newsletter
  account_fields:
  - keyname: list_id
    field_type: xhrSelect
    name: Audience ID
    description: Mailing List (or Audience) ID
    placeholder: ed2352c0cb
  - keyname: dashboard_type
    field_type: select
    options:
    - Recent Activity
    - Growth History
    name: Dashboard Type
    description: Type of data you want displayed
  category:
  - marketing
  - kpi
  refresh_every: 15
  cron: "*/6 * * * *"
  image: mailchimp.svg
  image_dark: mailchimp--dark.svg
- id: 19
  name: ConvertKit
  description: Email newsletter analytics
  active: true
  keyname: convertkit_analytics
  form_type: form_input
  plugin_type: native
  name_placeholder: Newsletter
  account_fields:
  - keyname: api_key
    field_type: string
    name: API Key
    description: Account API Key
    placeholder: ckv1_abc123def456ghi789jkl
    encrypted: true
  - keyname: api_secret
    field_type: string
    name: API Secret
    description: Account API Secret
    placeholder: sec_ckv1_789jkl456ghi123defabc890mno
    encrypted: true
  - keyname: duration
    field_type: select
    options:
    - '7'
    - '30'
    - '90'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - marketing
  - kpi
  refresh_every: 15
  cron: "*/6 * * * *"
  image: convertkit.svg
- id: 20
  name: Kickstarter
  description: Top crowdfunding projects
  active: true
  global: true
  keyname: kickstarter
  form_type: form_input
  plugin_type: native
  name_placeholder: New Products
  account_fields:
  - keyname: project_type
    field_type: select
    options:
    - Top
    - Ending Soon
    name: Projects Type
    description: Select the type of projects you are interested in?
  category:
  - life
  - discovery
  refresh_every: 60
  hide_refresh_interval: true
  cron: "*/3 * * * *"
  image: kickstarter.svg
- id: 21
  name: Teachable
  description: Online school analytics
  active: false
  keyname: teachable_analytics
  form_type: form_input
  plugin_type: native
  name_placeholder: My Courses
  account_fields: []
  category:
  - kpi
  - education
  - marketing
  refresh_every: 15
  cron: "*/3 * * * *"
  image: teachable.svg
- id: 22
  name: Postmark
  description: Transactional email analytics
  active: true
  keyname: postmark_analytics
  form_type: form_input
  plugin_type: native
  name_placeholder: Email Health
  account_fields:
  - keyname: server_token
    field_type: string
    name: Server API Token
    description: Server API Token
    placeholder: 4f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u
    encrypted: true
  - keyname: duration
    field_type: select
    options:
    - '7'
    - '30'
    - '90'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - kpi
  - email
  - marketing
  refresh_every: 15
  cron: "*/6 * * * *"
  image: postmark.svg
- id: 23
  name: Poetry Today
  description: Read a poem of the day
  active: true
  global: true
  keyname: poetry_today
  form_type: form_input
  plugin_type: native
  name_placeholder: Rhymes and Such
  account_fields: []
  category:
  - life
  - discovery
  refresh_every: 1440
  hide_refresh_interval: true
  cron: "*/3 * * * *"
  image: poetry_today.svg
- id: 24
  name: RSS Feed
  description: Subscribe to your favorite feed
  active: true
  keyname: rss_feed
  form_type: form_input
  plugin_type: native
  name_placeholder: Blog Feed
  account_fields:
  - keyname: url
    field_type: url
    name: Web Address
    description: URL of RSS Feed
    placeholder: https://www.site.com/rss
    help_text: Please include http:// or https://
  - keyname: layout
    field_type: select
    options:
    - Featured
    - List
    default: featured
    name: Layout
    description: Specify the preferred layout.
    help_text: "<b>Featured view</b> - Lets you view the topmost post </br> <b>List
      view</b> - Lets you view multiple posts."
  category:
  - life
  - discovery
  refresh_every: 60
  cron: "*/1 * * * *"
  image: rss.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-rss-feed
- id: 25
  name: Email Meter
  description: Inbox analytics for Gmail
  active: true
  keyname: email_meter
  form_type: form_input
  plugin_type: native
  name_placeholder: My Inbox
  account_fields:
  - keyname: api_token
    field_type: string
    name: API Token
    placeholder: 80bd14zzec3d10f167327957
    description: Account API Token
    encrypted: true
  - keyname: lookback_period
    field_type: select
    options:
    - '1'
    - '7'
    - '30'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - kpi
  - email
  refresh_every: 360
  hide_refresh_interval: true
  cron: "*/6 * * * *"
  image: email-meter.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-email-meter
- id: 26
  name: Close
  description: Close CRM analytics
  active: true
  keyname: close
  form_type: oauth2
  plugin_type: native
  name_placeholder: Opportunity Pipeline
  account_fields:
  - keyname: close_pipeline
    field_type: xhrSelect
    name: Pipeline Statuses
    description: Select up to 3 statuses
    multiple: true
    help_text: Hold cmd or ctrl to choose multiple
  - keyname: lookback_period
    field_type: select
    options:
    - '7'
    - '14'
    - '30'
    name: Duration
    description: Time span of displayed data
    help_text: In days
  category:
  - crm
  - marketing
  - sales
  - kpi
  refresh_every: 15
  cron: "*/6 * * * *"
  image: close.svg
- id: 27
  name: Square POS
  description: Analytics from Square POS
  active: true
  global: false
  keyname: square_pos
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Square Shop
  account_fields:
  - keyname: square_location
    field_type: xhrSelect
    name: Locations
    description: Select your Square POS Locations.
    help_text: You can select up to 3 locations
    multiple: true
  - keyname: lookback_period
    name: Lookback Period
    description: Time span of displayed transactions
    field_type: select
    options:
    - Today
    - Last 24 hours
    - Last 7 days
    default: today
  category:
  - ecommerce
  - sales
  - kpi
  refresh_every: 15
  cron: "*/6 * * * *"
  image: square.svg
  image_dark: square--dark.svg
- id: 28
  name: Weather
  description: Local weather now
  active: true
  global: false
  keyname: weather
  form_type: form_input
  plugin_type: native
  name_placeholder: My Town
  account_fields:
  - keyname: data_provider
    field_type: select
    options:
    - Tempest
    - WeatherAPI
    default: tempest
    name: Data Provider
    description: Provides underlying metrics and layout design.
    conditional_validation:
    - when: tempest
      required:
      - lat_lon
      hidden:
      - location
    - when: weatherapi
      required:
      - location
      hidden:
      - lat_lon
      - tempest_station_id
  - keyname: lat_lon
    field_type: string
    name: Latitude/Longitude (Tempest only)
    placeholder: "-28.001499,153.428467"
    description: Provide your location using comma-separated lat/long.
    help_text: Figure this out <a href="https://www.latlong.net/" class="underline"
      target="_blank">here</a>.
    optional: true
  - keyname: tempest_station_id
    field_type: number
    name: Station ID (Tempest only)
    placeholder: '12345'
    description: Optional. If you prefer to cherry-pick a station, find one <a href="https://tempestwx.com/map" target="_blank" class="underline">here</a>.
    help_text: The Station ID will be in the URL after /map/, for example "12345" in "/map/12345/xxx/yyy".
    optional: true
  - keyname: location
    field_type: string
    name: Location (WeatherAPI only)
    placeholder: Atlanta, GA, USA
    description: Provide a location
    help_text: 'Please be precise. Examples: </br> Paris, France (City/country) </br>
      10101 (Pass US Zipcode, UK Postcode, Canada Postal code)</br> 33.7501,84.3885
      (Lat/long)</br>'
    optional: true
  - keyname: units
    field_type: select
    options:
    - Imperial
    - Metric
    name: Temperature Unit
    description: Format in which to display the temperature
  - keyname: forecast_headings
    field_type: select
    options:
    - Today/Tomorrow
    - Absolute Date
    default: today/tomorrow
    name: Forecast Headings
    description: Whether to show as "today/tomorrow" or actual dates.
  - keyname: units_wind
    field_type: select
    options:
    - mph
    - kph
    - kts
    - mps
    - bft
    - lfm
    name: Wind Unit
    description: Format in which to display the wind speed
  - keyname: units_precip
    field_type: select
    options:
    - in
    - cm
    - mm
    name: Precipitation Unit
    description: Format in which to display precipitation
  category:
  - life
  - news
  refresh_every: 15
  cron: "*/1 * * * *"
  image: weather.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-weather
- id: 29
  name: Bible Verses
  description: Random scripture and bible verses
  active: true
  global: true
  keyname: bible_verses
  form_type: form_input
  plugin_type: native
  name_placeholder: Word of God
  account_fields:
  - keyname: bible
    field_type: select
    options:
    - American Standard
    - King James
    - World English Bible
    name: Version
    description: Pick a bible
  category:
  - life
  refresh_every: 1440
  cron: "*/3 * * * *"
  image: bible_verses.svg
- id: 30
  name: MLB Scores
  description: Baseball scoreboards and highlights
  active: false
  global: false
  keyname: mlb_scores
  form_type: form_input
  plugin_type: native
  name_placeholder: Game Stats
  account_fields:
  - keyname: mlb_team_id
    field_type: xhrSelect
    name: Team Name
    description: Choose a team to follow
    placeholder: Atlanta Braves
  category:
  - news
  refresh_every: 120
  cron: "*/3 * * * *"
  image: mlb_scores.svg
- id: 31
  name: Language Learning
  description: Foreign language word of the day
  active: true
  global: false
  keyname: language_learning
  form_type: form_input
  plugin_type: native
  name_placeholder: Japanese
  account_fields:
  - keyname: language
    field_type: select
    name: Language
    options:
    - Brazilian Portuguese
    - Danish
    - Dutch
    - English
    - French
    - Greek
    - Italian
    - Japanese
    - Korean
    - Norweigan
    - Spanish
    - Swedish
    - Turkish
    description: What language do you want to learn?
  category:
  - education
  refresh_every: 720
  hide_refresh_interval: true
  cron: "*/3 * * * *"
  image: language_learning.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-language-learning
- id: 32
  name: Image Display
  description: Display any image
  active: true
  global: false
  keyname: image_display
  form_type: form_input
  plugin_type: native
  no_screen_padding: true
  name_placeholder: My Image
  account_fields:
  - keyname: image_url
    field_type: url
    name: Image Address
    description: Please enter the image url, for best performance please ensure that
      the image is of size 800*480px.
    placeholder: https://example.com/image.png
  category:
  - programming
  - images
  refresh_every: 60
  cron: "*/3 * * * *"
  image: image_display.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-image-display
- id: 33
  name: Motivational Quote
  description: Motivational quote for the day
  active: true
  global: true
  keyname: motivational_quote
  form_type: form_input
  plugin_type: native
  account_fields: []
  hide_refresh_interval: true
  category:
  - life
  refresh_every: 60
  cron: "*/3 * * * *"
  image: motivational_quote.svg
- id: 34
  name: Screensaver
  description: Beautiful screensaver
  active: true
  global: true
  keyname: screensaver
  hide_refresh_interval: true
  form_type: form_input
  plugin_type: native
  no_screen_padding: true
  account_fields: []
  category:
  - life
  - images
  refresh_every: 360
  cron: "*/3 * * * *"
  image: screensaver.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-screensaver
- id: 35
  name: Lunch Money
  description: Personal finances dashboard
  active: true
  global: false
  keyname: lunch_money
  form_type: form_input
  plugin_type: native
  name_placeholder: Personal Finances
  account_fields:
  - keyname: access_token
    field_type: string
    name: Access Token
    description: Developer Access Token
    placeholder: 80bd14zzec3d10f167327957BdjS5Aj2kdDA
    encrypted: true
  - keyname: item_type
    field_type: select
    options:
    - Budgets
    - Accounts
    name: What financials do you want to see?
    description: If "budgets" is selected, per-category spending will be scoped month
      to date.
  category:
  - life
  refresh_every: 720
  cron: "*/6 * * * *"
  image: lunch_money.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9613508-lunch-money
- id: 36
  name: Days Left Until...
  description: Arbitrary countdown clock
  active: true
  global: false
  keyname: days_left_until
  form_type: form_input
  plugin_type: native
  name_placeholder: D-Day
  account_fields:
  - keyname: start_date
    field_type: date
    name: Start Date
    description: When should this counter begin?
    placeholder: yyyy-mm-dd
    help_text: Leave blank for today.
    optional: true
  - keyname: end_date
    field_type: date
    name: End Date
    description: When should this end?
    placeholder: yyyy-mm-dd
  - keyname: show_days_passed
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Show days passed?
    description: If "yes" is selected, a count-up number will be included.
  - keyname: show_days_left
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Show days remaining?
    description: If "yes" is selected, a count-down number will be included.
  category:
  - personal
  refresh_every: 1440
  cron: "*/6 * * * *"
  image: days_left_until.svg
- id: 37
  name: Private Plugin
  description: Build your own
  active: true
  global: false
  keyname: private_plugin
  form_type: form_input
  plugin_type: native
  name_placeholder: My Plugin
  account_fields:
  - keyname: strategy
    field_type: select
    options:
    - Polling
    - Webhook
    - Static
    default: polling
    name: Strategy
    description: How will data be shared with our server?
    help_text: Webhook - you will send data to TRMNL <strong>(maximum 2 kB)</strong>.<br>
      Polling - TRMNL will fetch data from a server.<br>
      Static - JSON merge variables (useful if data doesn't change), generates 1x screen /hr.
    conditional_validation:
    - when: polling
      hidden:
      - static_data
      - webhook_url
    - when: webhook
      hidden:
      - polling_url
      - polling_verb
      - polling_headers
      - polling_body
      - static_data
    - when: static
      hidden:
      - polling_url
      - polling_verb
      - polling_headers
      - polling_body
      - webhook_url
  - keyname: polling_url
    field_type: text
    name: Polling URL(s)
    description: "(Polling strategy only) From where should TRMNL fetch data? (JSON,
      XML, plaintext)"
    placeholder: https://myapp.ai/dashboard?api_key=xxx
    help_text: Optional unless 'Strategy' is 'Polling', supports multiple via line
      break separation
    optional: true
    previewable: true
    encrypted: true
  - keyname: polling_verb
    field_type: select
    name: Polling Verb
    description: How should our API request data?
    help_text: "(Polling strategy only)"
    options:
    - GET
    - POST
    default: get
    optional: true
  - keyname: polling_headers
    field_type: text
    rows: 3
    name: Polling Headers
    description: "(Polling strategy only) What HTTP headers should be included?"
    placeholder: authorization=bearer xxx&content-type=application/json
    help_text: Optional, useful for authorization or requesting specific formats
    optional: true
    encrypted: true
    previewable: true
  - keyname: polling_body
    field_type: text
    rows: 3
    name: Polling Body
    description: "(Polling strategy only) What body payload should be included?"
    placeholder: '{"sortBy":"date"}'
    help_text: Optional, useful for graphQL and non query-based requests
    optional: true
    encrypted: true
    previewable: true
  - keyname: custom_fields
    field_type: code
    rows: 8
    name: Form Fields
    description: Learn how to build form fields <a href="https://help.usetrmnl.com/en/articles/10513740-custom-plugin-form-builder"
      class="underline" target="_blank">here</a>, or <a class="underline cursor-pointer verify_custom_fields">click here</a> to verify syntax inline.
    placeholder: |
      - keyname: lookback_period
        field_type: select
        options:
        - '1'
        - '7'
        - '14'
        - '30'
        name: Lookback Period
        description: Time span of displayed data
        help_text: In days
    help_text: Optional, useful for providing a code free user configuration
    optional: true
  - keyname: static_data
    field_type: code
    rows: 10
    name: Static Data
    description: "(Static strategy only) Input valid JSON data"
    placeholder: '{"upcoming_holidays":[{"date":"2025-02-14","name":"Holiday to Taj
      Mahal, India"},{"date":"2025-06-11","name":"Visiting Japan"},{"date":"2025-12-20","name":"Northern
      Lights Norway!"}]}'
    optional: true
  - keyname: no_screen_padding
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Remove bleed margin?
    description: Defaults no. If "yes" is selected, padding around your markup will
      be removed.
    optional: true
  - keyname: dark_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Enable Dark Mode?
    description: Inverts black/white pixels. Will modify entire screen. Add class
      "image" to img tags as needed.
  - keyname: webhook_url
    field_type: copyable
    name: Webhook URL
    value_method: webhook_url
    description: "(Webhook strategy only) Paste this URL into your webhook integration"
  category:
  - personal
  - custom
  refresh_every: 15
  cron: "*/1 * * * *"
  processor: firefox
  image: private_plugin.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9510536-private-plugins
- id: 38
  name: Screenshot
  description: Snap a website
  active: true
  global: false
  keyname: screenshot
  form_type: form_input
  plugin_type: native
  no_screen_padding: true
  name_placeholder: My Website
  ignore_persistence_data: false
  disallow_mashup: true
  account_fields:
  - keyname: url
    field_type: url
    name: URL
    description: Website to screenshot
    placeholder: https://my-dashboard.com
    help_text: Please include http:// or https:// in the URL.
  - keyname: headers
    field_type: text
    name: Headers
    description: What HTTP headers should be included?
    placeholder: authorization=bearer xxx&content-type=application/json
    help_text: Optional, useful for authorization or requesting specific formats
    optional: true
    encrypted: true
  - keyname: absolutize
    field_type: select
    options:
    - 'yes'
    - 'no'
    default: 'yes'
    name: Use absolute source paths?
    description: When enabled, TRMNL will fix relative paths like /photo.jpg to yoursite.com/photo.jpg.
    help_text: Recommend setting to 'yes' - applies to img and script tags
  category:
  - personal
  - images
  - programming
  refresh_every: 15
  cron: "*/3 * * * *"
  image: screenshot.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-screenshot
- id: 39
  name: Simple Analytics
  description: Privacy-first web analytics
  active: true
  global: false
  keyname: simple_analytics
  form_type: form_input
  plugin_type: native
  name_placeholder: My Website
  account_fields:
  - keyname: api_key
    field_type: string
    name: API Key
    description: Account API Key
    placeholder: sa_api_key_qwerty
    encrypted: true
  - keyname: domain
    field_type: string
    name: Domain
    description: Simple Analytics domain - no "http"
    placeholder: usetrmnl.com
  - keyname: lookback_period
    field_type: select
    options:
    - '1'
    - '7'
    - '14'
    - '30'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - marketing
  - kpi
  - web-analytics
  refresh_every: 60
  cron: "*/6 * * * *"
  image: simple_analytics.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9575334-simple-analytics
- id: 40
  name: Clicky
  description: Privacy-friendly analytics
  active: true
  global: false
  keyname: clicky
  form_type: form_input
  plugin_type: native
  name_placeholder: My Site
  account_fields:
  - keyname: site_id
    field_type: string
    name: Site ID
    description: Site has its own unique numeric ID
    placeholder: *********
  - keyname: sitekey
    field_type: string
    name: Site Key
    description: Sitekey is a 12-16 character string of random letters and numbers
    placeholder: c12d3d4567cf89e0
    encrypted: true
  - keyname: lookback_period
    field_type: select
    options:
    - '1'
    - '7'
    - '14'
    - '30'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  category:
  - kpi
  - marketing
  - web-analytics
  refresh_every: 15
  cron: "*/6 * * * *"
  image: clicky.png
- id: 41
  name: ChatGPT
  description: Find answers and inspiration
  active: true
  global: false
  keyname: chatgpt
  form_type: form_input
  plugin_type: native
  name_placeholder: My Question
  account_fields:
  - keyname: api_key
    field_type: string
    name: API Key
    description: Account API Key
    placeholder: sk-proj-BscadWekfQgh52zVi4qjTkllmhFJ1ffbsbWWvdYYlif352TYT0
    encrypted: true
  - keyname: prompt
    field_type: text
    name: Prompt
    description: Enter your prompt for Chat GPT to respond.
    placeholder: Tell me a Dad Joke under 200 words.
  - keyname: model
    field_type: select
    options:
    - gpt-4o
    - gpt-4o-mini
    - gpt-4.5
    - gpt-4.1
    - gpt-3.5 Turbo
    - o3
    - o3-mini
    - o3-mini-high
    - o4-mini
    default: gpt-4o
    name: ChatGPT Model
    description: Select the model to be used.
  category:
  - personal
  - productivity
  refresh_every: 60
  cron: "*/6 * * * *"
  image: chatgpt.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-chatgpt
- id: 42
  name: Readwise
  description: Learn from your ebook & highlights
  active: true
  global: false
  keyname: readwise
  form_type: form_input
  plugin_type: native
  name_placeholder: Readwise Account
  account_fields:
  - keyname: access_token
    field_type: string
    name: Access Token
    description: Access Token (https://readwise.io/access_token)
    placeholder: 3S7DnrpNtNNBTLqwT5thEpYEZSfdOsagJBk1gfBCdqssUjm4qq
    encrypted: true
  category:
  - education
  refresh_every: 60
  cron: "*/6 * * * *"
  image: readwise.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9787214-readwise
- id: 43
  name: The Office
  description: American mockumentary TV series
  active: true
  global: true
  keyname: the_office
  form_type: form_input
  plugin_type: native
  name_placeholder: The Office
  account_fields: []
  category:
  - life
  refresh_every: 1440
  cron: "*/3 * * * *"
  image: the_office.jpg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9654172-the-office
- id: 44
  name: Google Analytics
  description: Free Web analytics
  active: true
  global: false
  keyname: google_analytics
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Website
  account_fields:
  - keyname: property_id
    field_type: string
    name: Property ID
    description: Account Property ID
    placeholder: *********
    encrypted: true
  - keyname: lookback_period
    field_type: select
    options:
    - '7'
    - '30'
    - '90'
    default: '7'
    name: Lookback Period
    description: Time span of displayed data
    help_text: In days
  - keyname: lookback_until
    field_type: select
    options:
    - Yesterday
    - Today
    default: yesterday
    name: Lookback Until
    description: End date of displayed data
    help_text: To prevent a visual 'drop-off' for current day, choose Yesterday.
    optional: true
  category:
  - marketing
  - kpi
  - web-analytics
  refresh_every: 60
  cron: "*/3 * * * *"
  image: google-analytics.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9922832-google-analytics
- id: 45
  name: Polymarket
  description: Decentralized prediction market
  active: true
  global: false
  keyname: polymarket
  form_type: form_input
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: polymarket_tag_id
    field_type: xhrSelect
    name: Tag
    description: Choose a tag to follow
    placeholder: All
  category:
  - news
  refresh_every: 30
  cron: "*/6 * * * *"
  image: polymarket.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9643261-polymarket
- id: 46
  name: HubSpot
  description: CRM Software
  active: true
  global: false
  keyname: hubspot
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Website Account
  account_fields:
  - keyname: hubspot_pipeline_id
    field_type: xhrSelect
    name: Pipeline Name
    description: Select a sales pipeline by name.
  - keyname: hubspot_deal_stages
    field_type: xhrSelect
    multiple: true
    name: Deal Stages
    description: Select up to 3.
    help_text: Hold cmd or ctrl to choose multiple
  category:
  - crm
  - marketing
  - sales
  - kpi
  refresh_every: 60
  cron: "*/6 * * * *"
  image: hubspot.svg
- id: 47
  name: Clock
  description: The time, sort of
  active: true
  global: false
  keyname: clock
  form_type: form_input
  plugin_type: native
  no_screen_padding: true
  name_placeholder: Clock
  ignore_persistence_data: true
  account_fields:
  - keyname: time_zone
    field_type: time_zone
    name: Time Zone
    description: Where are you located?
  category:
  - personal
  refresh_every: 30
  cron: "*/1 * * * *"
  image: clock.svg
- id: 48
  name: Custom Text
  description: Phrases important to you
  active: true
  global: false
  keyname: custom_text
  form_type: form_input
  plugin_type: native
  name_placeholder: Idea
  account_fields:
  - keyname: phrases
    field_type: text
    name: Phrases
    description: Make it count
    placeholder: I am the best; TRMNL helps me focus
    help_text: Input ideas you want to be reminded of, separated by a semi-colon
    encrypted: true
  - keyname: font_scaling
    field_type: select
    options:
    - Automatic
    - Fixed
    default: automatic
    name: Font scaling
    description: Choose Automatic if your phrase is more than 1-2 sentences long.
    encrypted: true
  - keyname: text_alignment
    field_type: select
    name: Text Alignment
    description: (Optional) How to align the text. Defaults to center.
    options:
    - Left
    - Center
    - Right
    default: center
    optional: true
  - keyname: title_bar_text
    field_type: string
    name: Title bar content
    description: "(Optional) What should appear in the bottom left corner?"
    placeholder: Daily Affirmations
    help_text: Leave blank to hide the title bar
    optional: true
    encrypted: true
  category:
  - life
  refresh_every: 60
  cron: "*/3 * * * *"
  image: custom_text.svg
- id: 49
  name: beehiiv
  description: Newsletter platform
  active: true
  global: false
  keyname: beehiiv
  form_type: form_input
  plugin_type: native
  name_placeholder: Newsletter Name
  account_fields:
  - keyname: api_key
    field_type: string
    name: API key
    description: API key from your beehiiv newsletter.
    placeholder: YpcIAQ51c4e06823844d84fe00522372278de8
    encrypted: true
  - keyname: publication_id
    field_type: string
    name: Publication
    placeholder: pub_6039gf60-348a-4a2d-82af-das8e2d66baa
    description: Enter the Publication you want analytics of
  category:
  - marketing
  - kpi
  refresh_every: 60
  cron: "*/6 * * * *"
  image: beehiiv.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9883235-beehiiv
- id: 50
  name: Todo List
  description: Stuff to focus on
  active: true
  global: false
  keyname: todo_list
  form_type: form_input
  plugin_type: native
  name_placeholder: Chores
  account_fields:
  - keyname: doing
    field_type: text
    name: Doing
    description: Outstanding tasks
    help_text: Separate items with a line break
    placeholder: |-
      Clean my room
      write a blog post
    encrypted: true
  - keyname: done
    field_type: text
    name: Done
    description: Completed tasks
    help_text: Separate items with a line break
    placeholder: |-
      Pay last year's taxes
      Buy TRMNL for a friend
    optional: true
    encrypted: true
  category:
  - productivity
  refresh_every: 15
  cron: "*/3 * * * *"
  image: todo_list.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9977738-todo-list
- id: 51
  name: Shopping List
  description: Stuff to buy
  active: true
  global: false
  keyname: shopping_list
  form_type: form_input
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: items
    field_type: text
    name: Items
    description: Things to purchase
    help_text: Separate items with a line break
    placeholder: |-
      Seed oils
      Diet Coke
      Cocktail shrimp
    encrypted: true
  category:
  - life
  refresh_every: 60
  cron: "*/3 * * * *"
  image: shopping_list.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9977739-shopping-list
- id: 52
  name: Upcoming Movies
  description: Coming soon to theaters
  active: true
  global: false
  keyname: upcoming_movies
  form_type: form_input
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: upcoming_movies_region
    field_type: xhrSelect
    name: Region
    description: Select your location
  - keyname: filter_by
    field_type: select
    options:
    - Upcoming
    - Now Playing
    name: Filter By
    description: Select a movie type
  category:
  - personal
  refresh_every: 60
  cron: "*/3 * * * *"
  image: upcoming_movies.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9977737-upcoming-movies
- id: 53
  name: Salesforce
  description: CRM analytics
  active: true
  global: false
  keyname: salesforce
  form_type: oauth2
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: salesforce_opportunity_views
    field_type: xhrSelect
    name: Opportunity Views
    description: Select up to 3 types
    multiple: true
    help_text: Hold cmd or ctrl to choose multiple
  - keyname: lookback_period
    field_type: select
    options:
    - '7'
    - '14'
    - '30'
    name: Duration
    description: Time span of displayed data
    help_text: In days
  category:
  - sales
  refresh_every: 60
  cron: "*/3 * * * *"
  image: salesforce.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9986527-salesforce
- id: 54
  name: Statuspage
  description: Service outage transparency
  active: true
  global: false
  keyname: statuspage
  form_type: form_input
  disallow_mashup: true
  plugin_type: native
  name_placeholder: Dependencies
  account_fields:
  - keyname: page_urls
    field_type: text
    name: Status Page URLs
    description: 'Must be accessible via RSS (ex: /history.rss)'
    help_text: Separate page URLs with a line break
    placeholder: |-
      https://www.stripestatus.com
      https://status.twilio.com
  category:
  - kpi
  - web-analytics
  refresh_every: 15
  cron: "*/3 * * * *"
  image: statuspage.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/9987937-statuspage
- id: 55
  name: Outlook Calendar
  description: Know your upcoming schedule
  active: true
  global: false
  keyname: outlook_calendar
  no_screen_padding: true
  form_type: form_input
  plugin_type: native
  name_placeholder: Personal Calendar
  account_fields:
  - keyname: ics_url
    field_type: text
    name: ICS Urls
    description: Published .ics calendar endpoints.
    placeholder: https://outlook.live.com/owa/calendar/00/98ea/cid-FE/calendar.ics
    help_text: Separate calendars with a line break
    encrypted: true
  - keyname: event_layout
    field_type: select
    options:
    - Agenda (today only): today_only
    - Default: default
    - Week: week
    - Month: month
    - Rolling Month: rolling_month
    - Schedule (minimal): schedule
    name: Layout
    description: How do you want to visualize events?
    default: default
    conditional_validation:
    - when: week
      hidden:
      - include_event_time
    - when: month
      hidden:
      - scroll_time
      - scroll_time_end
      - fixed_week
    - when: today_only
      hidden:
      - first_day
      - fixed_week
      - scroll_time
      - scroll_time_end
      - include_event_time
    - when: schedule
      hidden:
      - first_day
      - fixed_week
      - scroll_time
      - scroll_time_end
      - include_event_time
    - when: rolling_month
      hidden:
      - scroll_time
      - scroll_time_end
      - fixed_week
  - keyname: time_format
    field_type: select
    options:
    - AM/PM
    - 24 hrs
    default: am/pm
    name: Time Format
    description: Select preferred time format.
  - keyname: date_format
    name: Date Format
    description: Select prefered date heading format. Used in Default + Agenda layouts.
    help_text: Column headings in Week + Month layouts will observe your Locale from the Account tab.
    field_type: select
    options:
    - Localized (short): short
    - Thursday, November 26: '%A, %B %-d'
    - Thu, Nov 26: '%a, %b %-d'
    - 26 November: '%-d %B'
    - November 26: '%B %-d'
    - Nov 26: '%b %-d'
    - 26 Nov: '%-d %b'
    - '11-26': '%-m-%-d'
    - '11/26': '%-m/%-d'
    - '26/11': '%-d/%-m'
    default: short
  - keyname: include_description
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Include description
    description: Whether or not to show a preview of the event details.
  - keyname: include_event_time
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Include event time
    description: Whether or not to show an event's start/end time.
    help_text: Applies to month view only. Default, agenda, and week layouts include
      times by default.
  - keyname: include_past_events
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Include past events?
    description: Whether or not to show events that happened before today. Recommend
      disabling for Month layout.
  - keyname: first_day
    field_type: select
    options:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    default: monday
    name: Week start day
    description: Which day of the week should be the first?
    help_text: Applies to month view and week view (if 'Fixed week' is set to 'Yes').
  - keyname: fixed_week
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Fixed week?
    description: Whether you want to see columns for previous week days.
    help_text: Applies to week view only.
  - keyname: scroll_time
    field_type: time
    name: Fixed start time?
    description: By default, the week's earliest event time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: scroll_time_end
    field_type: time
    name: Fixed end time?
    description: By default, the week's latest event end time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: ignore_phrases
    field_type: text
    name: Ignored Phrases
    description: Ignore events with these words or phrases, separated by a comma
    placeholder: busy, personal errand, TODO
    help_text: Event title and description will both be considered.
    optional: true
  - keyname: event_status_filter
    field_type: select
    options:
    - All
    - Confirmed Only
    default: all
    name: Event Status Filter
    description: Whether to see all events, or only those you accepted.
  - keyname: zoom_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Zoom mode?
    description: Whether or not to increase event title font sizes and start/end time
      label contrast.
  category:
  - calendar
  - personal
  - productivity
  - life
  refresh_every: 15
  cron: "*/3 * * * *"
  image: outlook_calendar.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-outlook-calendar
- id: 56
  name: Days Since...
  description: Arbitrary countup clock
  active: true
  global: false
  keyname: days_since
  form_type: form_input
  plugin_type: native
  name_placeholder: I Quit
  account_fields:
  - keyname: start_date
    field_type: date
    placeholder: '2025-11-26'
    name: Start Date
    description: Provide your start date
    encrypted: true
  - keyname: start_date_format
    field_type: select
    options:
    - November 26, 2024
    - '2024-11-26'
    - Nov 26, 2024
    - 26 November 2024
    - 11/26/2024
    name: Date Format
    description: Pick a formatting style
  category:
  - personal
  refresh_every: 1440
  cron: "*/3 * * * *"
  image: days_since.svg
- id: 57
  name: CoinMarketCap
  description: Crypto token prices
  active: true
  global: false
  keyname: coinmarketcap
  form_type: form_input
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: api_token
    field_type: string
    name: API Key
    description: CoinMarketCap API Key
    placeholder: 51322-b181-43b1-bf40-c6a9e7534
    help_text: Get one free at https://pro.coinmarketcap.com/account
    encrypted: true
  - keyname: symbol
    field_type: string
    name: Token Symbols
    placeholder: BTC, FTX, HAWK
    description: Enter your preferred crypto symbols, separated by a comma.
  - keyname: currency
    field_type: select
    name: Currency
    options:
    - USD
    - CAD
    - CNY
    - EUR
    - GBP
    - INR
    - JPY
    - KRW
    - ZAR
    description: Base price (USD) will be multiplied by <= 1 day old exchange rate.
  category:
  - news
  refresh_every: 10
  cron: "*/3 * * * *"
  image: coinmarketcap.svg
  image_dark: coinmarketcap--dark.svg
- id: 58
  name: Apple Calendar
  description: Know your upcoming schedule
  active: true
  global: false
  keyname: apple_calendar
  form_type: form_input
  plugin_type: native
  name_placeholder: Personal Calendar
  account_fields:
  - keyname: ics_url
    field_type: text
    name: ICS Urls
    description: Published .ics calendar endpoints.
    placeholder: https://p44-caldav.icloud.com/published/2/MjY2O_mNMZ-kLFY3MCIT4q9cwN8VvbC_Kha_nU
    help_text: Separate calendars with a line break
    encrypted: true
  - keyname: event_layout
    field_type: select
    options:
    - Agenda (today only): today_only
    - Default: default
    - Week: week
    - Month: month
    - Rolling Month: rolling_month
    - Schedule (minimal): schedule
    name: Layout
    description: How do you want to visualize events?
    default: default
  - keyname: time_format
    field_type: select
    options:
    - AM/PM
    - 24 hrs
    default: am/pm
    name: Time Format
    description: Select preferred time format.
  - keyname: date_format
    name: Date Format
    description: Select prefered date heading format. Used in Default + Agenda layouts.
    help_text: Column headings in Week + Month layouts will observe your Locale from the Account tab.
    field_type: select
    options:
    - Localized (short): short
    - Thursday, November 26: '%A, %B %-d'
    - Thu, Nov 26: '%a, %b %-d'
    - 26 November: '%-d %B'
    - November 26: '%B %-d'
    - Nov 26: '%b %-d'
    - 26 Nov: '%-d %b'
    - '11-26': '%-m-%-d'
    - '11/26': '%-m/%-d'
    - '26/11': '%-d/%-m'
    default: short
  - keyname: include_description
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Include description
    description: Whether or not to show a preview of the event details.
  - keyname: include_event_time
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Include event time
    description: Whether or not to show an event's start/end time.
    help_text: Applies to month view only. Default, agenda, and week layouts include
      times by default.
  - keyname: include_past_events
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Include past events?
    description: Whether or not to show events that happened before today. Recommend
      disabling for Month layout.
  - keyname: first_day
    field_type: select
    options:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    default: monday
    name: Week start day
    description: Which day of the week should be the first?
    help_text: Applies to month view and week view (if 'Fixed week' is set to 'Yes').
  - keyname: fixed_week
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Fixed week?
    description: Whether you want to see columns for previous week days.
    help_text: Applies to week view only.
  - keyname: scroll_time
    field_type: time
    name: Fixed start time?
    description: By default, the week's earliest event time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: scroll_time_end
    field_type: time
    name: Fixed end time?
    description: By default, the week's latest event end time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: ignore_phrases
    field_type: text
    name: Ignored Phrases
    description: Ignore events with these words or phrases, separated by a comma
    placeholder: busy, personal errand, TODO
    help_text: Event title and description will both be considered.
    optional: true
  - keyname: event_status_filter
    field_type: select
    options:
    - All
    - Confirmed Only
    default: all
    name: Event Status Filter
    description: Whether to see all events, or only those you accepted.
  - keyname: zoom_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Zoom mode?
    description: Whether or not to increase event title font sizes and start/end time
      label contrast.
  category:
  - calendar
  - personal
  - productivity
  - life
  refresh_every: 30
  cron: "*/1 * * * *"
  image: apple_calendar.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-apple-calendar
- id: 59
  name: Fastmail Calendar
  description: Know your upcoming schedule
  active: true
  global: false
  keyname: fastmail_calendar
  form_type: form_input
  plugin_type: native
  name_placeholder: Personal Calendar
  account_fields:
  - keyname: ics_url
    field_type: text
    name: ICS Urls
    description: Published .ics calendar endpoints.
    placeholder: https://user.fm/calendar/v1-2e8f44404a1095bc9f71d/Calendar.ics
    help_text: Separate calendars with a line break
    encrypted: true
  - keyname: event_layout
    field_type: select
    options:
    - Agenda (today only): today_only
    - Default: default
    - Week: week
    - Month: month
    - Rolling Month: rolling_month
    - Schedule (minimal): schedule
    name: Layout
    description: How do you want to visualize events?
    default: default
  - keyname: time_format
    field_type: select
    options:
    - AM/PM
    - 24 hrs
    default: am/pm
    name: Time Format
    description: Select preferred time format.
  - keyname: date_format
    name: Date Format
    description: Select prefered date heading format. Used in Default + Agenda layouts.
    help_text: Column headings in Week + Month layouts will observe your Locale from the Account tab.
    field_type: select
    options:
    - Localized (short): short
    - Thursday, November 26: '%A, %B %-d'
    - Thu, Nov 26: '%a, %b %-d'
    - 26 November: '%-d %B'
    - November 26: '%B %-d'
    - Nov 26: '%b %-d'
    - 26 Nov: '%-d %b'
    - '11-26': '%-m-%-d'
    - '11/26': '%-m/%-d'
    - '26/11': '%-d/%-m'
    default: short
  - keyname: include_description
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Include description
    description: Whether or not to show a preview of the event details.
  - keyname: include_event_time
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Include event time
    description: Whether or not to show an event's start/end time.
    help_text: Applies to month view only. Default, agenda, and week layouts include
      times by default.
  - keyname: include_past_events
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Include past events?
    description: Whether or not to show events that happened before today. Recommend
      disabling for Month layout.
  - keyname: first_day
    field_type: select
    options:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    default: monday
    name: Week start day
    description: Which day of the week should be the first?
    help_text: Applies to month view and week view (if 'Fixed week' is set to 'Yes').
  - keyname: fixed_week
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Fixed week?
    description: Whether you want to see columns for previous week days.
    help_text: Applies to week view only.
  - keyname: scroll_time
    field_type: time
    name: Fixed start time?
    description: By default, the week's earliest event time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: scroll_time_end
    field_type: time
    name: Fixed end time?
    description: By default, the week's latest event end time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: ignore_phrases
    field_type: text
    name: Ignored Phrases
    description: Ignore events with these words or phrases, separated by a comma
    placeholder: busy, personal errand, TODO
    help_text: Event title and description will both be considered.
    optional: true
  - keyname: event_status_filter
    field_type: select
    options:
    - All
    - Confirmed Only
    default: all
    name: Event Status Filter
    description: Whether to see all events, or only those you accepted.
  - keyname: zoom_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Zoom mode?
    description: Whether or not to increase event title font sizes and start/end time
      label contrast.
  category:
  - calendar
  - personal
  - productivity
  - life
  refresh_every: 30
  cron: "*/3 * * * *"
  image: fastmail_calendar.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-fastmail-calendar
- id: 60
  name: Nextcloud Calendar
  description: Know your upcoming schedule
  active: true
  global: false
  keyname: nextcloud_calendar
  form_type: form_input
  plugin_type: native
  name_placeholder: Personal Calendar
  account_fields:
  - keyname: ics_url
    field_type: text
    name: ICS Urls
    description: Published .ics calendar endpoints.
    placeholder: https://cloud.your-domain.com/remote.php/dav/public-calendars/qwerty12345?export
    help_text: Separate calendars with a line break
    encrypted: true
  - keyname: event_layout
    field_type: select
    options:
    - Agenda (today only): today_only
    - Default: default
    - Week: week
    - Month: month
    - Rolling Month: rolling_month
    - Schedule (minimal): schedule
    name: Layout
    description: How do you want to visualize events?
    default: default
  - keyname: time_format
    field_type: select
    options:
    - AM/PM
    - 24 hrs
    default: am/pm
    name: Time Format
    description: Select preferred time format.
  - keyname: date_format
    name: Date Format
    description: Select prefered date heading format. Used in Default + Agenda layouts.
    help_text: Column headings in Week + Month layouts will observe your Locale from the Account tab.
    field_type: select
    options:
    - Localized (short): short
    - Thursday, November 26: '%A, %B %-d'
    - Thu, Nov 26: '%a, %b %-d'
    - 26 November: '%-d %B'
    - November 26: '%B %-d'
    - Nov 26: '%b %-d'
    - 26 Nov: '%-d %b'
    - '11-26': '%-m-%-d'
    - '11/26': '%-m/%-d'
    - '26/11': '%-d/%-m'
    default: short
  - keyname: include_description
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Include description
    description: Whether or not to show a preview of the event details.
  - keyname: include_event_time
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Include event time
    description: Whether or not to show an event's start/end time.
    help_text: Applies to month view only. Default, agenda, and week layouts include
      times by default.
  - keyname: include_past_events
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Include past events?
    description: Whether or not to show events that happened before today. Recommend
      disabling for Month layout.
  - keyname: first_day
    field_type: select
    options:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    default: monday
    name: Week start day
    description: Which day of the week should be the first?
    help_text: Applies to month view and week view (if 'Fixed week' is set to 'Yes').
  - keyname: fixed_week
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Fixed week?
    description: Whether you want to see columns for previous week days.
    help_text: Applies to week view only.
  - keyname: scroll_time
    field_type: time
    name: Fixed start time?
    description: By default, the week's earliest event time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: scroll_time_end
    field_type: time
    name: Fixed end time?
    description: By default, the week's latest event end time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: ignore_phrases
    field_type: text
    name: Ignored Phrases
    description: Ignore events with these words or phrases, separated by a comma
    placeholder: busy, personal errand, TODO
    help_text: Event title and description will both be considered.
    optional: true
  - keyname: event_status_filter
    field_type: select
    options:
    - All
    - Confirmed Only
    default: all
    name: Event Status Filter
    description: Whether to see all events, or only those you accepted.
  - keyname: zoom_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Zoom mode?
    description: Whether or not to increase event title font sizes and start/end time
      label contrast.
  category:
  - calendar
  - personal
  - productivity
  - life
  refresh_every: 30
  cron: "*/6 * * * *"
  image: nextcloud_calendar.jpeg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-nextcloud-calendar
- id: 61
  name: Tempest Weather Station
  description: Home weather system
  active: true
  global: false
  keyname: tempest_weather_station
  form_type: oauth2
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: tempest_weather_station_devices
    field_type: xhrSelect
    name: Weather Station
    description: Tempest device you'd like to connect
  - keyname: units
    field_type: select
    options:
    - Imperial
    - Metric
    name: Temperature Unit
    description: Format in which to display the temperature
  - keyname: forecast_headings
    field_type: select
    options:
    - Today/Tomorrow
    - Absolute Date
    default: today/tomorrow
    name: Forecast Headings
    description: Whether to show as "today/tomorrow" or actual dates.
  - keyname: units_wind
    field_type: select
    options:
    - mph
    - kph
    - kts
    - mps
    - bft
    - lfm
    name: Wind Unit
    description: Format in which to display the wind speed
  - keyname: units_precip
    field_type: select
    options:
    - in
    - cm
    - mm
    name: Precipitation Unit
    description: Format in which to display precipitation
  category:
  - life
  - news
  refresh_every: 15
  cron: "*/3 * * * *"
  image: tempest_weather_station.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-tempest-weather-station
- id: 62
  name: Eight Sleep
  description: Sleep better
  active: true
  global: false
  keyname: eight_sleep
  form_type: form_input
  disallow_mashup: true
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: email
    field_type: string
    name: Email
    description: Mobile app login username
    placeholder: <EMAIL>
  - keyname: password
    field_type: password
    name: Password
    description: Mobile app login password
    placeholder: s3cret!
    encrypted: true
  category:
  - kpi
  - life
  - personal
  refresh_every: 1440
  cron: "*/6 * * * *"
  image: eight_sleep.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-eight-sleep
- id: 63
  name: Lunar Calendar
  description: Track lunar phases and moon cycles
  active: true
  global: false
  keyname: lunar_calendar
  form_type: form_input
  plugin_type: native
  category:
  - life
  - personal
  refresh_every: 720
  cron: "*/3 * * * *"
  name_placeholder: Lunar Calendar
  account_fields: []
  image: lunar_calendar.svg
- id: 64
  name: Mondrian
  description: Piet Mondrian style generative art
  active: true
  global: true
  keyname: mondrian
  form_type: form_input
  plugin_type: native
  no_screen_padding: true
  name_placeholder: Mondrian
  account_fields: []
  category:
  - life
  - images
  refresh_every: 360
  cron: "*/3 * * * *"
  image: mondrian.svg
- id: 65
  name: Todoist
  description: Todo list management app
  active: true
  global: false
  keyname: todoist
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Tasks
  account_fields:
  - keyname: todoist_project_id
    field_type: xhrSelect
    name: Todoist Project
    description: Select Todoist Project
    encrypted: true
  - keyname: sort_grouping
    name: Sort/Grouping
    description: How to group tasks in the task list.
    field_type: select
    options:
    - None
    - Date
    - Date Added
    - Deadline
    - Priority
    - Label
    default: none
  - keyname: sort_sorting
    name: Sort/Sorting
    description: How to sort tasks in the task list.
    field_type: select
    options:
      - Manual
      - Name
      - Date
      - Date Added
      - Deadline
      - Priority
    default: date
  - keyname: sort_direction
    name: Sort/Direction
    description: The direction to sort tasks in the task list.
    field_type: select
    options:
      - Ascending
      - Descending
    default: ascending
  - keyname: filter_completed_tasks
    name: Completed Tasks
    description: To show completed tasks in the task list.
    field_type: select
    options:
    - Yes
    - No
    default: no
  - keyname: filter_date
    name: Filter/Date
    description: Filter tasks by date in the task list.
    field_type: select
    options:
      - All          # nil
      - Today        # `overdue | today`
      - This Week    # `due before: next week`
      - Next 7 Days  # `overdue | next 7 days`
      - This month   # `due before: first day`
      - Next 30 Days # `overdue | next 30 days`
      - No date      # `no date`
    default: all
  - keyname: filter_deadline
    name: Filter/Deadline
    description: Filter tasks by deadline in the task list.
    field_type: select
    options:
      - All
      - Today
      - This Week
      - Next 7 Days
      - This month
      - Next 30 Days
      - No deadline
    default: all
  - keyname: filter_priority
    name: Filter/Priority
    description: Filter tasks by priority in the task list.
    field_type: select
    options:
      - Priority 1: p1
      - Priority 2: p2
      - Priority 3: p3
      - Priority 4: p4
    multiple: true
    optional: true
  - keyname: todoist_filter_label_ids
    name: Filter/Label
    description: Filter tasks by labels.
    field_type: xhrSelect
    multiple: true
    optional: true
  category:
  - productivity
  refresh_every: 60
  cron: "*/3 * * * *"
  image: todoist.svg
- id: 66
  name: TickTick
  description: Personal task management
  active: true
  global: false
  keyname: ticktick
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Tasks
  account_fields:
  - keyname: ticktick_project_id
    field_type: xhrSelect
    name: Ticktick Project
    description: Select Ticktick Project
    encrypted: true
  category:
  - productivity
  refresh_every: '60'
  cron: "*/3 * * * *"
  image: ticktick.svg
- id: 67
  name: Google Tasks
  description: Google task management
  active: true
  global: false
  keyname: google_tasks
  form_type: oauth2
  plugin_type: native
  name_placeholder: My Tasks
  account_fields:
  - keyname: google_tasks_list
    field_type: xhrSelect
    name: Task List
    description: Select Task List
  category:
  - productivity
  refresh_every: 15
  cron: "*/3 * * * *"
  image: google_tasks.svg
- id: 68
  name: Parcel
  description: Package tracking
  active: true
  global: false
  keyname: parcel
  form_type: form_input
  plugin_type: native
  name_placeholder: My Deliveries
  account_fields:
  - keyname: api_key
    field_type: string
    name: API Key
    description: Generate at https://web.parcelapp.net/
    placeholder: _foobar123
    encrypted: true
  - keyname: filter_mode
    field_type: select
    name: Filter mode
    description: Select which deliveries to display
    options:
    - Recent
    - Active
    default: active
  - keyname: style
    field_type: select
    name: Style
    description: How much information to display for each delivery
    options:
    - Detailed
    - Compact
    default: detailed
  - keyname: empty_state
    field_type: select
    name: Empty State
    description: When there are no deliveries, should the screen be shown?
    options:
    - Display
    - Skip
    default: display
  category:
  - personal
  refresh_every: 60
  cron: "*/3 * * * *"
  image: parcel.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-parcel
- id: 69
  name: Alias
  description: Use your own image
  active: true
  global: false
  keyname: alias
  form_type: form_input
  plugin_type: native
  name_placeholder: My Image
  disallow_mashup: true
  hide_refresh_interval: true
  account_fields:
  - keyname: type
    field_type: select
    name: Type
    description: Please select whether the URL is in plain text or encrypted format.
    options:
    - Plain Text
    - Encrypted (Coming Soon)
    default: plain_text
  - keyname: url
    field_type: url
    name: URL
    description: Absolute path of the image URL.
    placeholder: http://***********/secret.png
    help_text: Can be local or in the cloud, must be accessible from your device's WiFi network.
    encrypted: true
  - keyname: enable_caching
    name: Enable cache?
    field_type: select
    options:
    - "Yes"
    - "No"
    default: "yes"
    description: If set to 'no', your device will always re-draw the image URL, even if its filename matches the last rendered filename. Useful if your playlist is only a single Alias item, as the filename is used for cache busting.
  category:
  - personal
  - kpi
  refresh_every: 14400
  cron: "*/30 * * * *"
  image: alias.png
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-alias-plugin
- id: 70
  name: Basecamp
  description: Project management software
  active: true
  global: false
  keyname: basecamp
  form_type: oauth2
  plugin_type: native
  name_placeholder: New Instance
  account_fields:
  - keyname: basecamp_account
    field_type: xhrSelect
    name: Basecamp Account
    description: Select Basecamp Account
  - keyname: basecamp_project
    field_type: xhrSelect
    name: Project
    description: Select Project
  category:
  - productivity
  refresh_every: '60'
  cron: "*/6 * * * *"
  image: basecamp.svg
- id: 71
  name: CalDAV
  description: Custom calendar
  active: true
  global: false
  keyname: caldav
  form_type: form_input
  plugin_type: native
  name_placeholder: Custom calendar
  account_fields:
  - keyname: ics_url
    field_type: text
    name: ICS Urls
    description: Published .ics calendar endpoints.
    placeholder: https://user.fm/calendar/v1-2e8f44404a1095bc9f71d/Calendar.ics
    help_text: Separate calendars with a line break
    encrypted: true
  - keyname: headers
    field_type: text
    rows: 3
    name: Headers
    description: What HTTP headers should be included?
    placeholder: authorization=bearer xxx&content-type=application/json
    help_text: Input as key=value&key=value; use '%3D' if '=' sign is needed
    optional: true
    encrypted: true
  - keyname: event_layout
    field_type: select
    options:
    - Agenda (today only): today_only
    - Default: default
    - Week: week
    - Month: month
    - Rolling Month: rolling_month
    - Schedule (minimal): schedule
    name: Layout
    description: How do you want to visualize events?
    default: default
  - keyname: time_format
    field_type: select
    options:
    - AM/PM
    - 24 hrs
    default: am/pm
    name: Time Format
    description: Select preferred time format.
  - keyname: date_format
    name: Date Format
    description: Select prefered date heading format. Used in Default + Agenda layouts.
    help_text: Column headings in Week + Month layouts will observe your Locale from the Account tab.
    field_type: select
    options:
    - Localized (short): short
    - Thursday, November 26: '%A, %B %-d'
    - Thu, Nov 26: '%a, %b %-d'
    - 26 November: '%-d %B'
    - November 26: '%B %-d'
    - Nov 26: '%b %-d'
    - 26 Nov: '%-d %b'
    - '11-26': '%-m-%-d'
    - '11/26': '%-m/%-d'
    - '26/11': '%-d/%-m'
    default: short
  - keyname: include_description
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'yes'
    name: Include description
    description: Whether or not to show a preview of the event details.
  - keyname: include_event_time
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Include event time
    description: Whether or not to show an event's start/end time.
    help_text: Applies to month view only. Default, agenda, and week layouts include
      times by default.
  - keyname: include_past_events
    field_type: select
    options:
    - 'Yes'
    - 'No'
    name: Include past events?
    description: Whether or not to show events that happened before today. Recommend
      disabling for Month layout.
  - keyname: first_day
    field_type: select
    options:
    - Sunday
    - Monday
    - Tuesday
    - Wednesday
    - Thursday
    - Friday
    - Saturday
    default: monday
    name: Week start day
    description: Which day of the week should be the first?
    help_text: Applies to month view and week view (if 'Fixed week' is set to 'Yes').
  - keyname: fixed_week
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Fixed week?
    description: Whether you want to see columns for previous week days.
    help_text: Applies to week view only.
  - keyname: scroll_time
    field_type: time
    name: Fixed start time?
    description: By default, the week's earliest event time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: scroll_time_end
    field_type: time
    name: Fixed end time?
    description: By default, the week's latest event end time will be used as an anchor.
      Override that here.
    help_text: Optional. Must be on the hour. Applies to week view only.
    step: 3600
    optional: true
  - keyname: ignore_phrases
    field_type: text
    name: Ignored Phrases
    description: Ignore events with these words or phrases, separated by a comma
    placeholder: busy, personal errand, TODO
    help_text: Event title and description will both be considered.
    optional: true
  - keyname: event_status_filter
    field_type: select
    options:
    - All
    - Confirmed Only
    default: all
    name: Event Status Filter
    description: Whether to see all events, or only those you accepted.
  - keyname: zoom_mode
    field_type: select
    options:
    - 'Yes'
    - 'No'
    default: 'no'
    name: Zoom mode?
    description: Whether or not to increase event title font sizes and start/end time
      label contrast.
  category:
  - calendar
  - personal
  - productivity
  - life
  refresh_every: 30
  cron: "*/3 * * * *"
  image: caldav.svg
- id: 72
  name: Redirect
  description: Redirect to your data
  active: true
  global: false
  keyname: redirect
  form_type: form_input
  plugin_type: native
  name_placeholder: New Instance
  hide_refresh_interval: true
  account_fields:
  - keyname: url
    field_type: url
    name: Web Address
    description: JSON URL endpoint
    placeholder: https://www.mysite.com/trmnl/display.json?api_key=9fbb0844-51a1-454f-90e6-4a8bd8b42749
    help_text: Please include http:// or https://
  category:
  - programming
  refresh_every: 14400
  cron: "*/30 * * * *"
  image: redirect.svg
  disallow_mashup: true
  knowledge_base_url: https://help.usetrmnl.com/en/articles/********-redirect-plugin
- id: 73
  name: Changelog
  description: News from TRMNL
  active: true
  global: true
  keyname: changelog
  form_type: form_input
  plugin_type: native
  name_placeholder: News
  account_fields: []
  category:
  - news
  refresh_every: '720'
  cron: "*/3 * * * *"
  image: changelog.svg
- id: 74
  name: Notion
  description: Display Notion databases and pages
  active: true
  global: false
  keyname: notion
  form_type: oauth2
  plugin_type: native
  name_placeholder: Database
  account_fields:
  - keyname: display_type
    name: Content Type
    description: What to display from Notion
    field_type: select
    options:
    - Database: database
    - Page: page
    default: database
    conditional_validation:
    - when: database
      required:
      - notion_database_id
      hidden:
      - notion_page_id
      - image_height
    - when: page
      required:
      - notion_page_id
      hidden:
      - notion_database_id
      - title_field
      - status_field
      - sort_property
      - sort_direction
      - labeled_properties
      - listed_properties
  - keyname: notion_database_id
    field_type: xhrSelectSearch
    name: Database
    description: Search and select a Notion database
    placeholder: Select or search databases...
    optional: true
  - keyname: notion_page_id
    field_type: xhrSelectSearch
    name: Page
    description: Search and select a Notion page
    placeholder: Select or search pages...
    optional: true
  - keyname: max_items
    name: Item Limit
    description: Maximum items to show
    field_type: number
    default: 20
    optional: true
    min: 1
    max: 100
  - keyname: image_height
    name: Image Height
    description: Maximum height for images in pixels
    field_type: number
    default: 120
    optional: true
    help_text: Set maximum image height in pixels
  - keyname: title_field
    name: Title Field
    description: Which property to use as the item title
    field_type: text
    optional: true
    placeholder: Name
    default: Name
    help_text: Name of the property to display as the item title (e.g., "Name", "Title")
  - keyname: status_field
    name: Status Field
    description: Which property to use as the primary status indicator
    field_type: text
    optional: true
    placeholder: Status
    help_text: Name of the property to display as a prominent badge (e.g., "Status", "Priority", "State")
  - keyname: labeled_properties
    name: Labeled Properties
    description: Comma-separated list of properties to display as badges with alternating styles
    field_type: text
    optional: true
    placeholder: Status, Priority, Due Date
    help_text: Enter property names to display as badges at the top of each item. They will be styled with alternating patterns for visual variety.
  - keyname: listed_properties
    name: Listed Properties
    description: Comma-separated list of properties to display as a list below the title and badges
    field_type: text
    optional: true
    placeholder: Description, Tags, Author
    help_text: Enter property names to display as a list below the title and badges. These will show additional details for each item.
  - keyname: sort_property
    name: Sort By
    description: Primary property to sort by
    field_type: text
    optional: true
    placeholder: Due Date
    help_text: Property name to sort by, or use "created_time" or "last_edited_time" for timestamps
  - keyname: sort_direction
    name: Sort Direction
    description: Sort direction for primary property
    field_type: select
    options:
    - Ascending: ascending
    - Descending: descending
    default: descending
  - keyname: multi_column_display
    name: Multi-Column Display
    description: Display items in multiple columns (for half_horizontal and full layouts)
    field_type: select
    options:
    - Single Column: '1'
    - Two Columns: '2'
    default: '2'
    optional: true
    help_text: Enable multi-column layout for better space utilization on wider displays
  category:
  - productivity
  refresh_every: 60
  cron: "*/3 * * * *"
  image: notion.svg
  image_dark: notion--dark.svg
  knowledge_base_url: https://help.usetrmnl.com/en/articles/11882090-notion
- id: 75
  active: false
- id: 76
  active: false
- id: 77
  active: false
- id: 78
  active: false
- id: 79
  active: false
- id: 80
  active: false
- id: 81
  active: false
- id: 82
  active: false
- id: 83
  active: false
- id: 84
  active: false
- id: 85
  active: false
- id: 86
  active: false
- id: 87
  active: false
- id: 88
  active: false
- id: 89
  active: false
- id: 90
  active: false
- id: 91
  active: false
- id: 92
  active: false
- id: 93
  active: false
- id: 94
  active: false
- id: 95
  active: false
- id: 96
  active: false
- id: 97
  active: false
- id: 98
  active: false
- id: 99
  active: false
- id: 100
  active: false
