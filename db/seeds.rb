puts "Generating announcements..."
announcements = [
  {
    title: 'Introducing Our New App!',
    body: 'Discover exciting features and enhancements that are new with this update.',
    created_at: Date.parse('2024-04-05T00:00:00')
  },
  {
    title: 'Download Now and Explore',
    body: 'Get the app on your device and start exploring. There are new features to check out!',
    created_at: Date.parse('2024-04-06T00:00:00')
  },
  {
    title: 'Behind the Scenes',
    body: 'Learn about the tech stack and engineering that went into creating this product.',
    created_at: Date.parse('2024-04-07T00:00:00')
  }
]
announcements.each { |attributes| Announcement.find_or_create_by! attributes } if Announcement.count.zero?

puts "syncing plugins..."
system('bin/rails plugins:sync')

# Need this to run tests, as these are constant data. Do not delete.
DeviceModel.all_model_specs.each do |model|
  DeviceModel.upsert(model, unique_by: :id)
end
