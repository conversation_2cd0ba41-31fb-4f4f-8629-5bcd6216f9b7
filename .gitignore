# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep
/public/*/.DS_Store
dump.rdb

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep
coverage
/public/assets

# Ignore master key for decrypting credentials and more.
/config/master.key

/app/assets/builds/*
!/app/assets/builds/.keep

# JetBrains IDE (RubyMine) specifics
.idea

/config/credentials/development.key
/config/credentials/production.key

# Ignore DS_Store files anywhere
**/.DS_Store

# Ignore locally generated sitemap
public/sitemap.xml.gz
public/sitemap.xml
.claude/settings.local.json
.byebug_history
.vscode/settings.json
