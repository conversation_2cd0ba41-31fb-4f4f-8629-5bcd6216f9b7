---
openapi: 3.0.1
info:
  title: TRMNL API
  version: '1'
paths:
  "/display":
    get:
      summary: Fetch the next screen
      tags:
      - Device API
      parameters:
      - name: Access-Token
        in: header
        required: true
        description: Device API Key (eg. abc-123)
        schema:
          type: string
      - name: Battery-Voltage
        in: header
        required: false
        description: Device battery voltage (eg. 3.7)
        schema:
          type: number
      - name: FW-Version
        in: header
        required: false
        description: Device firmware version (eg. 0.0.1)
        schema:
          type: string
      - name: RSSI
        in: header
        required: false
        description: Device RSSI (eg. -69)
        schema:
          type: number
      - name: Height
        in: header
        required: false
        description: Device screen height (eg. 480)
        schema:
          type: string
      - name: Width
        in: header
        required: false
        description: Device screen width (eg. 800)
        schema:
          type: string
      - name: Special-Function
        in: header
        required: false
        description: Device special function (eg. true)
        schema:
          type: boolean
      - name: BASE64
        in: header
        required: false
        description: Encode image function (eg. true)
        schema:
          type: boolean
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  image_url:
                    type: string
                    nullable: true
                    example: https://usetrmnl.com/images/setup/setup-logo.bmp
                  filename:
                    type: string
                    nullable: true
                    example: setup-logo.bmp
                  refresh_rate:
                    type: integer
                    example: 300
                  reset_firmware:
                    type: boolean
                    example: false
                  update_firmware:
                    type: boolean
                    example: false
                  firmware_url:
                    type: string
                    nullable: true
                    example: https://usetrmnl.com/firmware/1.0.0.bin
                  special_function:
                    type: string
                    nullable: false
                    example: identify
                  action:
                    type: string
                    nullable: true
                    example: identify
  "/log":
    post:
      summary: Log with logs[] (array)
      tags:
      - Device API
      parameters:
      - name: Access-Token
        in: header
        required: true
        description: Device API Key (eg. abc-123)
        schema:
          type: string
      responses:
        '204':
          description: Logs created
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                logs: []
              required:
              - logs
              additionalProperties: false
        required: true
        description: 'An array of log entries. Each entry can be any JSON type: string,
          object, etc.'
  "/setup":
    get:
      summary: Set up device
      tags:
      - Device API
      parameters:
      - name: ID
        in: header
        required: true
        description: Device MAC Address (eg. 41:B4:10:39:A1:24)
        schema:
          type: string
      description: |
        Please note that the returned `status` JSON value may NOT always equal the HTTP status code. Notably, if a device MAC address is not found,
        then the HTTP status code will be 200 but the `status` code in the response will be 404.
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 200
                  api_key:
                    type: string
                    nullable: true
                    example: abc-123
                  friendly_id:
                    type: string
                    nullable: true
                    example: ABC-123
                  image_url:
                    type: string
                    nullable: true
                    example: https://usetrmnl.com/images/setup/setup-logo.bmp
                  message:
                    type: string
                    example: Register at usetrmnl.com/signup with Device ID 'ABC-123'
  "/models":
    get:
      summary: List all device models
      tags:
      - Models
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Model"
  "/devices":
    get:
      summary: List my devices
      tags:
      - Devices
      security:
      - bearer_auth: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/Device"
  "/devices/{id}":
    parameters:
    - name: id
      in: path
      description: Device ID
      required: true
      schema:
        type: integer
    get:
      summary: Get the data of a device
      tags:
      - Devices
      security:
      - bearer_auth: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/Device"
        '404':
          description: Not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/playlists/items":
    get:
      summary: List my playlist items
      tags:
      - Playlists
      security:
      - bearer_auth: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/PlaylistItem"
  "/playlists/items/{id}":
    patch:
      summary: Update a playlist item
      tags:
      - Playlists
      parameters:
      - name: id
        in: path
        description: ID of the playlist item
        required: true
        schema:
          type: integer
      security:
      - bearer_auth: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Updated
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                visible:
                  type: boolean
              required:
              - visible
  "/plugin_settings/{id}/archive":
    parameters:
    - name: id
      in: path
      description: Plugin setting ID
      required: true
      schema:
        type: integer
    get:
      summary: Download a plugin setting archive
      tags:
      - Plugin Settings
      security:
      - bearer_auth: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
    post:
      summary: Upload a plugin setting archive
      tags:
      - Plugin Settings
      security:
      - bearer_auth: []
      parameters: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/PluginSettingArchive"
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: file
        required: true
        description: Plugin setting archive file
  "/plugin_settings/{id}/data":
    parameters:
    - name: id
      in: path
      description: Plugin setting ID
      required: true
      schema:
        type: integer
    get:
      summary: Get the data of a plugin setting
      tags:
      - Plugin Settings
      security:
      - bearer_auth: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
        '404':
          description: Not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '422':
          description: Data is not available
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
  "/plugin_settings":
    post:
      summary: Create a new plugin setting
      tags:
      - Plugin Settings
      security:
      - bearer_auth: []
      parameters: []
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/PluginSetting"
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/PluginSettingParams"
        required: true
  "/plugin_settings/{id}":
    delete:
      summary: Delete a plugin setting
      tags:
      - Plugin Settings
      security:
      - bearer_auth: []
      parameters:
      - name: id
        in: path
        required: true
        description: ID of the plugin setting to delete
        schema:
          type: integer
      responses:
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
        '204':
          description: Deleted
        '404':
          description: Not found
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/Error"
servers:
- url: https://{defaultHost}/api
  variables:
    defaultHost:
      default: usetrmnl.com
components:
  securitySchemes:
    bearer_auth:
      type: http
      scheme: bearer
      bearerFormat: Account API key
  schemas:
    Error:
      type: object
      additionalProperties: false
      properties:
        error:
          type: string
          example: An error occurred
    Device:
      type: object
      additionalProperties: false
      properties:
        id:
          type: integer
          example: 123
        name:
          type: string
          example: My TRMNL
        friendly_id:
          type: string
          example: ABC-123
        mac_address:
          type: string
          example: 12:34:56:78:9A:BC
        battery_voltage:
          type: number
          nullable: true
          example: 3.7
        rssi:
          type: integer
          nullable: true
          example: -70
        percent_charged:
          type: number
          example: 85.0
          minimum: 0
          maximum: 100
        wifi_strength:
          type: number
          example: 75.0
          minimum: 0
          maximum: 100
    Model:
      type: object
      additionalProperties: false
      properties:
        name:
          type: string
          example: trmnl_original
          description: Unique identifier
        label:
          type: string
          example: TRMNL
          description: Human-readable name
        description:
          type: string
          example: Original TRMNL model
          description: Description
        width:
          type: integer
          example: 800
          description: Screen width in pixels
        height:
          type: integer
          example: 480
          description: Screen height in pixels
        colors:
          type: integer
          example: 2
          description: Number of colors supported
        bit_depth:
          type: integer
          example: 1
          description: Color bit depth
        scale_factor:
          type: number
          example: 1.0
          description: Display scale factor
        rotation:
          type: integer
          example: 90
          description: Screen rotation in degrees
        mime_type:
          type: string
          example: image/png
          description: Image MIME type
        offset_x:
          type: integer
          example: 10
          description: X offset for image rendering
        offset_y:
          type: integer
          example: 20
          description: Y offset for image rendering
        published_at:
          type: string
          format: date_time
          example: '2023-10-01T12:00:00Z'
          description: Publication date in RFC 3339 format
    PlaylistItem:
      type: object
      additionalProperties: false
      properties:
        created_at:
          type: string
          format: date_time
          example: '2023-10-01T12:00:00Z'
        device_id:
          type: integer
          example: 1
        id:
          type: integer
          example: 1
        mashup_id:
          type: integer
          example: 1
          nullable: true
        mirror:
          type: boolean
          example: true
        playlist_group_id:
          type: integer
          example: 1
        plugin_setting:
          "$ref": "#/components/schemas/PluginSetting"
        plugin_setting_id:
          type: integer
          example: 1
        rendered_at:
          type: string
          format: date_time
          example: '2023-10-01T12:00:00Z'
        row_order:
          type: integer
          example: 1
        updated_at:
          type: string
          format: date_time
          example: '2023-10-01T12:00:00Z'
        visible:
          type: boolean
          example: true
    PlaylistItemParams:
      type: object
      additionalProperties: false
      properties:
        visible:
          type: boolean
          example: true
    PluginSetting:
      type: object
      additionalProperties: false
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: My Plugin Setting
        plugin_id:
          type: integer
          example: 1
    PluginSettingArchive:
      type: object
      properties:
        settings_yaml:
          type: string
          description: YAML settings file
    PluginSettingParams:
      type: object
      additionalProperties: false
      properties:
        name:
          type: string
          example: My Plugin Setting
          required: true
        plugin_id:
          type: integer
          example: 1
          required: true
